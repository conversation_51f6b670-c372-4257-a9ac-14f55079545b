# Firestore Index Setup Guide

## 🎉 Good News!

The error `cloud_firestore/failed-precondition` means your **Firebase rules and authentication are working correctly!** You just need to create the required database indexes.

## 🚨 Quick Fix

### Option 1: Use the Auto-Generated Link (Fastest)
Click the link provided in your error message:
```
https://console.firebase.google.com/v1/r/project/mcq-quiz-system/firestore/indexes?create_composite=...
```
This will automatically create the required index.

### Option 2: Use Deployment Script
```bash
# Run the index deployment script
./deploy-firebase-indexes.sh
```

### Option 3: Manual Creation via Firebase Console
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select project: `mcq-quiz-system`
3. Go to **Firestore Database** → **Indexes** tab
4. Click **Create Index**
5. Configure:
   - **Collection ID**: `exams`
   - **Field 1**: `isActive` (Ascending)
   - **Field 2**: `createdAt` (Descending)
6. Click **Create**

## 📋 Required Indexes

Your app needs these composite indexes:

### 1. Exams Collection - Basic Query
**Fields:**
- `isActive` (Ascending)
- `createdAt` (Descending)

**Used by:**
```dart
.where('isActive', isEqualTo: true)
.orderBy('createdAt', descending: true)
```

### 2. Exams Collection - Role-Based Query
**Fields:**
- `isActive` (Ascending)
- `suitableFor` (Array Contains)
- `createdAt` (Descending)

**Used by:**
```dart
.where('isActive', isEqualTo: true)
.where('suitableFor', arrayContains: role)
.orderBy('createdAt', descending: true)
```

## ⏳ Index Creation Process

### What Happens After Creating Indexes:
1. **Index Building**: Firebase starts building indexes in the background
2. **Time Required**: Can take minutes to hours depending on data size
3. **Status Monitoring**: Check progress in Firebase Console → Indexes
4. **Query Availability**: Queries work once indexing is complete

### Index Status Indicators:
- 🟡 **Building**: Index is being created
- 🟢 **Ready**: Index is complete and queries will work
- 🔴 **Error**: Index creation failed

## 🧪 Testing After Index Creation

### 1. Wait for Index Completion
Check Firebase Console → Firestore → Indexes until status shows "Ready"

### 2. Test Your App
```dart
// This query should now work
final exams = await ExamService.getActiveExams();
print('Found ${exams.length} exams');
```

### 3. Use Debug Tools
1. Go to Settings → Firebase Debug
2. Click "Full Diagnostics"
3. Look for: `✅ Exams collection read: (X docs)`

## 🔧 Troubleshooting

### If Index Creation Fails:

#### Check Permissions
```bash
firebase login
firebase projects:list
```

#### Verify Project
```bash
firebase use mcq-quiz-system
```

#### Manual Deployment
```bash
firebase deploy --only firestore:indexes
```

### If Queries Still Fail After Index Creation:

#### 1. Verify Index Status
- Go to Firebase Console → Firestore → Indexes
- Ensure all indexes show "Ready" status

#### 2. Check Query Structure
Make sure your queries match the index fields exactly:
```dart
// ✅ Correct - matches index
.where('isActive', isEqualTo: true)
.orderBy('createdAt', descending: true)

// ❌ Wrong - different field order
.orderBy('createdAt', descending: true)
.where('isActive', isEqualTo: true)
```

#### 3. Clear App Cache
Sometimes cached query plans cause issues:
```dart
// Force refresh
await FirebaseAuth.instance.signOut();
await FirebaseAuth.instance.signInWithCredential(credential);
```

## 📊 Monitoring Index Usage

### Firebase Console Monitoring:
1. Go to Firestore Database → Usage
2. Check "Index usage" section
3. Monitor query performance

### App-Level Monitoring:
```dart
// Add timing to your queries
final stopwatch = Stopwatch()..start();
final exams = await ExamService.getActiveExams();
stopwatch.stop();
print('Query took: ${stopwatch.elapsedMilliseconds}ms');
```

## 🚀 Performance Optimization

### Best Practices:
1. **Limit Results**: Use `.limit()` for large collections
2. **Pagination**: Implement cursor-based pagination
3. **Caching**: Cache frequently accessed data
4. **Selective Fields**: Only fetch needed fields

### Example Optimized Query:
```dart
// Instead of fetching all exams
final exams = await _firestore
    .collection('exams')
    .where('isActive', isEqualTo: true)
    .orderBy('createdAt', descending: true)
    .limit(10) // Limit results
    .get();
```

## 📋 Complete Index List

Here are all the indexes your app needs:

```json
{
  "indexes": [
    {
      "collectionGroup": "exams",
      "fields": [
        {"fieldPath": "isActive", "order": "ASCENDING"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "exams", 
      "fields": [
        {"fieldPath": "isActive", "order": "ASCENDING"},
        {"fieldPath": "suitableFor", "arrayConfig": "CONTAINS"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    }
  ]
}
```

## 🎯 Next Steps

1. **Create the required indexes** (using any of the 3 methods above)
2. **Wait for index completion** (monitor in Firebase Console)
3. **Test your app** - the permission denied error should be resolved
4. **Use debug tools** to verify everything is working
5. **Monitor performance** and optimize queries as needed

## ✅ Success Indicators

You'll know everything is working when:
- ✅ Firebase Console shows indexes as "Ready"
- ✅ Your app can fetch exams without errors
- ✅ Debug tools show successful collection access
- ✅ No more `failed-precondition` errors

The index error is actually good news - it means your authentication and rules are working correctly!
