#!/bin/bash

# Deploy Firebase Rules Script
# This script helps deploy the correct Firebase rules for the MCQ Quiz app

echo "🔥 Firebase Rules Deployment Script"
echo "=================================="

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed."
    echo "Please install it with: npm install -g firebase-tools"
    exit 1
fi

echo "✅ Firebase CLI found"

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase."
    echo "Please run: firebase login"
    exit 1
fi

echo "✅ Firebase authentication verified"

# Show available rule files
echo ""
echo "📋 Available rule files:"
echo "1. firebase/firestore.rules - Current rules (may be restrictive)"
echo "2. firebase/firestore.rules.simple - Simplified rules for debugging"
echo "3. firebase/firestore.rules.production - Production-ready rules with validation"
echo "4. firebase/firestore.rules.temp - Temporary open rules for testing"

echo ""
echo "🚨 IMPORTANT: For debugging the permission denied error, use option 2 or 4"
echo ""

# Ask user which rules to deploy
read -p "Which rules would you like to deploy? (1-4): " choice

case $choice in
    1)
        rules_file="firebase/firestore.rules"
        echo "📝 Using current rules"
        ;;
    2)
        rules_file="firebase/firestore.rules.simple"
        echo "📝 Using simplified rules (recommended for debugging)"
        ;;
    3)
        rules_file="firebase/firestore.rules.production"
        echo "📝 Using production rules"
        ;;
    4)
        rules_file="firebase/firestore.rules.temp"
        echo "📝 Using temporary open rules (most permissive)"
        ;;
    *)
        echo "❌ Invalid choice. Exiting."
        exit 1
        ;;
esac

# Check if the selected file exists
if [ ! -f "$rules_file" ]; then
    echo "❌ Rules file not found: $rules_file"
    exit 1
fi

echo "✅ Rules file found: $rules_file"

# Show current project
current_project=$(firebase use --project)
echo "🎯 Current Firebase project: $current_project"

# Confirm deployment
echo ""
echo "⚠️  You are about to deploy Firestore rules to: $current_project"
echo "📄 Rules file: $rules_file"
echo ""
read -p "Are you sure you want to continue? (y/N): " confirm

if [[ $confirm != [yY] && $confirm != [yY][eE][sS] ]]; then
    echo "❌ Deployment cancelled"
    exit 1
fi

# Backup current rules
echo "💾 Backing up current rules..."
cp firebase/firestore.rules firebase/firestore.rules.backup.$(date +%Y%m%d_%H%M%S)

# Copy selected rules to main rules file
echo "📋 Copying selected rules..."
cp "$rules_file" firebase/firestore.rules

# Deploy rules
echo "🚀 Deploying rules..."
if firebase deploy --only firestore:rules; then
    echo ""
    echo "✅ Rules deployed successfully!"
    echo ""
    echo "🔍 Next steps:"
    echo "1. Test your Flutter app to see if the permission denied error is resolved"
    echo "2. Use the debug tools in your app (Settings → Firebase Debug)"
    echo "3. Check the console output for detailed diagnostics"
    echo ""
    echo "📱 To test in your Flutter app:"
    echo "   - Go to Settings → Firebase Debug"
    echo "   - Click 'Full Diagnostics'"
    echo "   - Check console output for results"
    echo ""
else
    echo ""
    echo "❌ Rules deployment failed!"
    echo ""
    echo "🔧 Possible solutions:"
    echo "1. Check if you have permission to deploy to this project"
    echo "2. Verify you're logged in with the correct account: firebase login"
    echo "3. Check if the project ID is correct: firebase use --project"
    echo "4. Try deploying manually via Firebase Console"
    echo ""
    echo "📖 Manual deployment:"
    echo "1. Go to https://console.firebase.google.com"
    echo "2. Select your project: mcq-quiz-system"
    echo "3. Go to Firestore Database → Rules"
    echo "4. Copy the contents of: $rules_file"
    echo "5. Paste and publish the rules"
    
    # Restore backup
    echo "🔄 Restoring original rules..."
    git checkout firebase/firestore.rules 2>/dev/null || echo "⚠️  Could not restore original rules"
    
    exit 1
fi

echo ""
echo "🎉 Deployment complete!"
echo "📊 You can monitor rule usage in Firebase Console → Firestore → Usage"
