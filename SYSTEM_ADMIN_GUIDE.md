# 👑 System Admin Feature Guide

## 🎯 Overview
The System Admin feature provides a dedicated user management interface for users with the `system_admin` role. System admins can view, approve, edit, and manage all admin users in the system.

## ✨ Features Implemented

### **🔧 System Admin Dashboard Card**
- **Exclusive Access**: Only visible to users with `system_admin` role
- **User Management Interface**: Complete admin user management system
- **Real-time Updates**: Live user list with refresh capability

### **👥 User Management Features**
- ✅ **View All Admin Users**: Complete list of all registered admin users
- ✅ **Approve Pending Users**: Approve new admin registrations
- ✅ **Edit User Roles**: Change user roles (admin, super_admin, system_admin)
- ✅ **Activate/Deactivate**: Enable or disable user accounts
- ✅ **User Details**: View creation date, last login, status

### **🎨 UI Components**
- ✅ **Professional Table**: Clean, sortable user list
- ✅ **Action Buttons**: Approve, Edit, Deactivate options
- ✅ **Role Badges**: Color-coded role indicators
- ✅ **Status Chips**: Active/Pending status display
- ✅ **Edit Dialog**: Modal for editing user details

## 🏗️ System Architecture

### **Role Hierarchy**
```
System Admin (system_admin)
├── Can manage all admin users
├── Can approve/reject registrations
├── Can change user roles
└── Can activate/deactivate accounts

Super Admin (super_admin)
├── Can manage questions/categories
├── Can view analytics
└── Standard admin features

Admin (admin)
├── Can manage questions/categories
├── Can view basic analytics
└── Standard admin features
```

### **Database Structure**
```javascript
// admin_users collection
{
  uid: "firebase_user_id",
  email: "<EMAIL>",
  name: "Admin Name",
  role: "system_admin" | "super_admin" | "admin",
  createdAt: timestamp,
  lastLogin: timestamp,
  isActive: true | false
}
```

## 🚀 How to Use

### **Step 1: Create System Admin User**

#### **Option A: Using Script (Recommended)**
```bash
# Install dependencies if needed
cd web_admin
npm install firebase

# Create system admin user
node scripts/create-system-admin.js <EMAIL> admin123 "System Administrator"
```

#### **Option B: Manual Creation**
1. Register normally at `/register`
2. Manually update the user's role in Firestore:
   ```javascript
   // In Firestore console
   admin_users/{user_id}
   {
     role: "system_admin"
   }
   ```

### **Step 2: Login as System Admin**
```bash
# Navigate to login page
http://localhost:3000/login

# Use system admin credentials
Email: <EMAIL>
Password: admin123
```

### **Step 3: Access User Management**
1. **Login** with system admin credentials
2. **Navigate** to dashboard
3. **Find** the "🔧 System Administration" section
4. **View** the "👥 User Management" card

## 🎮 User Management Operations

### **📋 View Users**
- **All Users**: See complete list of admin users
- **User Details**: Name, email, role, status, creation date
- **Real-time**: Refresh button for latest data

### **✅ Approve Users**
```bash
# For pending users (isActive: false)
1. Click "Approve" button
2. User becomes active (isActive: true)
3. User can now login and access admin features
```

### **✏️ Edit Users**
```bash
# Edit user details
1. Click "Edit" button
2. Change role (admin/super_admin/system_admin)
3. Change status (active/inactive)
4. Save changes
```

### **🚫 Deactivate Users**
```bash
# For active users
1. Click "Deactivate" button
2. User becomes inactive (isActive: false)
3. User can no longer access admin features
```

## 🔒 Security Features

### **Firestore Rules**
```javascript
// System admins can manage all admin users
allow read, write: if request.auth != null && 
  exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
  get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.role in ['super_admin', 'system_admin'];
```

### **Role-based Access**
- ✅ **System Admin Only**: User management card only visible to system admins
- ✅ **Authentication Required**: All operations require valid authentication
- ✅ **Role Validation**: Server-side role checking in Firestore rules

## 🎨 UI Features

### **Role Color Coding**
- 🔴 **System Admin**: Red badge (`#ff5722`)
- 🟡 **Super Admin**: Gold badge
- 🔵 **Admin**: Blue badge

### **Status Indicators**
- 🟢 **Active**: Green chip
- 🟡 **Pending**: Yellow chip

### **Action Buttons**
- ✅ **Approve**: Green button for pending users
- ✏️ **Edit**: Blue outlined button
- 🚫 **Deactivate**: Red outlined button

## 🧪 Testing

### **Test User Approval Flow**
1. **Register** a new admin user (will be pending)
2. **Login** as system admin
3. **View** pending user in management table
4. **Approve** the user
5. **Verify** user can now login

### **Test Role Management**
1. **Edit** an existing user
2. **Change** their role to super_admin
3. **Save** changes
4. **Verify** role badge updates

### **Test Deactivation**
1. **Deactivate** an active user
2. **Try to login** as that user
3. **Verify** access is denied

## 📋 User Workflow

### **New User Registration**
```
1. User registers → Creates account with isActive: false
2. System admin sees pending user in management table
3. System admin approves user → isActive: true
4. User can now login and access admin features
```

### **Role Management**
```
1. System admin edits user
2. Changes role (admin → super_admin)
3. User gets updated permissions
4. Role badge updates in UI
```

## 🔧 Development

### **Add New User Actions**
```typescript
// In UserManagement.tsx
const handleCustomAction = async (userId: string) => {
  try {
    const userRef = doc(db, 'admin_users', userId);
    await updateDoc(userRef, {
      // Custom updates
    });
    toast.success('Action completed!');
    fetchUsers(); // Refresh list
  } catch (error) {
    toast.error('Action failed');
  }
};
```

### **Extend User Data**
```typescript
// Add new fields to AdminUser interface
interface AdminUser {
  // ... existing fields
  department?: string;
  permissions?: string[];
  lastActivity?: any;
}
```

## 🎉 Summary

The System Admin feature provides:
- ✅ **Complete User Management**: View, approve, edit, deactivate users
- ✅ **Role-based Access**: Secure, hierarchical permissions
- ✅ **Professional UI**: Clean, intuitive interface
- ✅ **Real-time Updates**: Live data with refresh capability
- ✅ **Security**: Firestore rules and authentication
- ✅ **Easy Setup**: Script for creating system admin users

**Ready to use**: Create a system admin user and start managing your admin team! 👑
