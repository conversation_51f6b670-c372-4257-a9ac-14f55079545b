# 🔍 Fix: New Users Not Displaying in User Management

## ❌ Problem
New users are not appearing in the User Management interface for system admins, even after registration.

## 🧪 Step-by-Step Diagnosis

### **Step 1: Check Current User Role**
1. **Login** as system admin
2. **Check header** - should show red "SYSTEM ADMIN" badge
3. **Open browser console** (F12) and look for:
   ```
   🔐 Current admin user: {role: "system_admin", email: "<EMAIL>"}
   ✅ UserManagement component rendering for role: system_admin
   ```

### **Step 2: Check Database Contents**
```bash
# Run diagnostic script
cd web_admin
node scripts/check-firestore-users.js
```

This will show:
- How many users exist in Firestore
- Their status (pending/approved/rejected)
- Who are system admins

### **Step 3: Test User Registration**
1. **Go to User Management page**: `http://localhost:3000/user-management`
2. **Use Test Registration tool** (development mode only)
3. **Create test user** with the form
4. **Check if user appears** in the table above

### **Step 4: Check Browser Console**
Look for these debug messages:
```
✅ UserManagement component rendering for role: system_admin
🔍 Fetching users from admin_users collection...
📊 Total documents found: X
👤 User found: {email: "<EMAIL>", status: "pending"}
📋 Final users list: X users
```

## 🔧 Common Issues & Solutions

### **Issue 1: Not System Admin**
**Symptoms**: Header shows "ADMIN" or "SUPER ADMIN" instead of "SYSTEM ADMIN"
**Solution**:
```bash
# Promote your account to system admin
node scripts/promote-to-system-admin.js <EMAIL>

# Or create new system admin
node scripts/create-system-admin.js <EMAIL> admin123 "System Administrator"
```

### **Issue 2: No Users Registered**
**Symptoms**: Diagnostic script shows 0 users
**Solution**:
```bash
# Option A: Use test registration tool
# Go to /user-management and use "Test User Registration" form

# Option B: Register manually
# Go to /register and create account normally
```

### **Issue 3: Firestore Rules Blocking**
**Symptoms**: Console shows permission denied errors
**Solution**:
```bash
# Deploy development rules
./scripts/deploy-firestore-rules.sh dev

# Or check Firebase Console → Firestore → Rules
```

### **Issue 4: Users Exist But Not Showing**
**Symptoms**: Diagnostic shows users exist, but table is empty
**Solution**:
```bash
# Check browser console for errors
# Look for Firestore permission errors
# Try hard refresh (Ctrl+Shift+R)
# Check network tab for failed requests
```

## 🎯 Quick Test Workflow

### **Complete Test from Scratch**
```bash
# 1. Create system admin
node scripts/create-system-admin.js <EMAIL> admin123 "System Administrator"

# 2. Check database
node scripts/check-firestore-users.js

# 3. Login as system admin
# Go to: http://localhost:3000/login
# Login: <EMAIL> / admin123

# 4. Go to User Management
# Navigate to: http://localhost:3000/user-management

# 5. Create test user
# Use "Test User Registration" form
# Email: <EMAIL>, Name: Test User

# 6. Verify user appears
# Should see test user in table with "Pending Approval" status
```

## 🔍 Debug Information Added

### **Enhanced User Management**
- ✅ **User count display**: Shows "X user(s) found"
- ✅ **Role verification**: Shows current user role
- ✅ **Console logging**: Detailed debug information
- ✅ **Test registration**: Built-in test user creation

### **Browser Console Messages**
```
✅ UserManagement component rendering for role: system_admin
🔍 Fetching users from admin_users collection...
📊 Total documents found: 2
👤 User found: {id: "abc123", email: "<EMAIL>", status: "pending"}
👤 User found: {id: "def456", email: "<EMAIL>", status: "approved"}
📋 Final users list: 2 users
```

## 📊 Expected Results

### **When Working Correctly**
- **User Management page** shows table with users
- **User count** displays "X user(s) found"
- **Pending users** have yellow "Pending Approval" status
- **Approve/Reject buttons** visible for pending users

### **Debug Information**
- **Console shows** successful user fetching
- **No permission errors** in browser console
- **Network tab** shows successful Firestore requests

## 🛠️ Manual Verification

### **Check Firebase Console**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select mcq-quiz-system project
3. Go to Firestore Database
4. Check admin_users collection
5. Verify documents exist

### **Expected Document Structure**
```javascript
// Pending user document
{
  uid: "firebase_user_id",
  email: "<EMAIL>",
  name: "Test User",
  role: "admin",
  createdAt: timestamp,
  isActive: false,
  status: "pending"
}
```

## 🎯 Success Checklist

- [ ] System admin role verified (red badge in header)
- [ ] User Management component renders
- [ ] Console shows successful user fetching
- [ ] User count displays correctly
- [ ] Test user creation works
- [ ] Users appear in table with correct status
- [ ] Approve/Reject buttons functional

## 📞 Still Not Working?

If users still don't appear after following this guide:

1. **Run diagnostic script**: `node scripts/check-firestore-users.js`
2. **Check browser console** for specific errors
3. **Verify Firestore rules** are deployed
4. **Test with fresh browser session**
5. **Check Firebase project** is active

The enhanced debugging will show exactly where the issue is occurring.
