import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Alert,
} from '@mui/material';
import { collection, addDoc, getDocs, Timestamp } from 'firebase/firestore';
import { db } from '../../config/firebase';
import toast from 'react-hot-toast';
import BulkUploadCard from '../../components/questions/BulkUploadCard';

interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation?: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
}

interface BulkUploadData {
  examName: string;
  examType: string;
  timeLimit: number;
  suitableFor: string[];
  questions: Question[];
}

const BulkUploadPage: React.FC = () => {
  const [examTypes, setExamTypes] = useState<string[]>([
    'Postal Guide',
    'Postal Volumes',
    'Custom Exam',
  ]);

  useEffect(() => {
    fetchExamTypes();
  }, []);

  const fetchExamTypes = async () => {
    try {
      const examTypesSnapshot = await getDocs(collection(db, 'examTypes'));
      const types: string[] = ['Postal Guide', 'Postal Volumes', 'Custom Exam'];

      examTypesSnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.name && !types.includes(data.name)) {
          types.push(data.name);
        }
      });

      setExamTypes(types);
    } catch (error) {
      console.error('Error fetching exam types:', error);
    }
  };

  const handleBulkUploadComplete = async (data: BulkUploadData) => {
    try {
      // Validation
      if (!data.examName || !data.examType || data.suitableFor.length === 0) {
        toast.error('Please fill in all exam configuration fields');
        return;
      }

      if (data.questions.length === 0) {
        toast.error('No questions found to upload');
        return;
      }

      // Check for duplicate exam name
      const existingExams = await getDocs(collection(db, 'exams'));
      const duplicateExam = existingExams.docs.find(doc =>
        doc.data().name.toLowerCase() === data.examName.toLowerCase()
      );

      if (duplicateExam) {
        toast.error(`An exam with the name "${data.examName}" already exists. Please choose a different name.`);
        return;
      }

      // Create the exam with questions
      const examData = {
        name: data.examName,
        examType: data.examType,
        numberOfQuestions: data.questions.length,
        timeLimit: data.timeLimit,
        suitableFor: data.suitableFor,
        questions: data.questions,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        isActive: true,
      };

      // Save exam to Firestore
      const examRef = await addDoc(collection(db, 'exams'), examData);

      // Also save each question individually to questions collection
      const questionPromises = data.questions.map(question => {
        const questionData = {
          ...question,
          examId: examRef.id,
          examName: data.examName,
          examType: data.examType,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
        };

        return addDoc(collection(db, 'questions'), questionData);
      });

      await Promise.all(questionPromises);

      toast.success(
        `🎉 Successfully created exam "${data.examName}" with ${data.questions.length} questions! ` +
        `The exam is now available in the Categories section and mobile app.`
      );

    } catch (error) {
      console.error('Error creating exam from bulk upload:', error);
      toast.error('Failed to create exam. Please try again.');
    }
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Bulk Upload Questions
      </Typography>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Upload multiple questions via CSV file and automatically create a new exam with comprehensive configuration options.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <BulkUploadCard
            onUploadComplete={handleBulkUploadComplete}
            examTypes={examTypes}
          />
        </Grid>

        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                📋 CSV Format Guide
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Your CSV file should include these columns:
              </Typography>
              <Box component="ul" sx={{ pl: 2, mb: 2 }}>
                <Typography component="li" variant="body2">
                  <strong>question</strong> - The question text
                </Typography>
                <Typography component="li" variant="body2">
                  <strong>option1, option2, option3, option4</strong> - Answer choices
                </Typography>
                <Typography component="li" variant="body2">
                  <strong>correct</strong> - Correct answer number (1-4)
                </Typography>
                <Typography component="li" variant="body2">
                  <strong>difficulty</strong> - Easy, Medium, or Hard (optional)
                </Typography>
                <Typography component="li" variant="body2">
                  <strong>explanation</strong> - Answer explanation (optional)
                </Typography>
              </Box>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Example CSV format:</strong>
                </Typography>
                <Box component="pre" sx={{ fontSize: '0.75rem', mt: 1, overflow: 'auto' }}>
{`question,option1,option2,option3,option4,correct,difficulty,explanation
"What is 2+2?","3","4","5","6",2,"Easy","Basic arithmetic"
"Capital of India?","Mumbai","Delhi","Kolkata","Chennai",2,"Medium","Delhi is the capital"`}
                </Box>
              </Alert>

              <Alert severity="success" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  The system will automatically create a new exam with your uploaded questions and the configuration you specify.
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default BulkUploadPage;
