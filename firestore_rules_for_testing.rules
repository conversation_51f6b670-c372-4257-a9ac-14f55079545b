rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read access to exams collection for all authenticated users
    match /exams/{examId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null; // For testing - restrict this in production
    }
    
    // Allow read/write access to users collection for authenticated users
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow admins to read/write everything (for admin panel)
    match /{document=**} {
      allow read, write: if request.auth != null && 
        exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // For testing - allow read access to test collection
    match /test/{testId} {
      allow read, write: if request.auth != null;
    }
  }
}

// Instructions for applying these rules:
// 1. Go to Firebase Console
// 2. Select your project
// 3. Go to Firestore Database
// 4. Click on "Rules" tab
// 5. Replace the existing rules with the above rules
// 6. Click "Publish"
//
// Note: These rules are permissive for testing purposes.
// In production, you should restrict write access to exams collection
// to only admin users.
