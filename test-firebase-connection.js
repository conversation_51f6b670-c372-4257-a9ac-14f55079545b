// Firebase Connection Test Script
// This script tests the Firebase configuration for both mobile and web

const fs = require('fs');
const path = require('path');

console.log('🔥 Firebase Connection Test');
console.log('==========================');

// Test results
let tests = {
  passed: 0,
  failed: 0,
  warnings: 0
};

function logTest(name, status, message = '') {
  const symbols = { pass: '✅', fail: '❌', warn: '⚠️' };
  console.log(`${symbols[status]} ${name}${message ? ': ' + message : ''}`);
  tests[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
}

// Test 1: Check Firebase project configuration
console.log('\n📋 Checking Firebase Project Configuration...');

try {
  const firebaserc = JSON.parse(fs.readFileSync('.firebaserc', 'utf8'));
  if (firebaserc.projects && firebaserc.projects.default === 'mcq-quiz-system') {
    logTest('Firebase project ID', 'pass', 'mcq-quiz-system');
  } else {
    logTest('Firebase project ID', 'fail', 'incorrect or missing');
  }
} catch (error) {
  logTest('Firebase project configuration', 'fail', '.firebaserc not found or invalid');
}

try {
  const firebaseJson = JSON.parse(fs.readFileSync('firebase/firebase.json', 'utf8'));
  if (firebaseJson.emulators) {
    logTest('Firebase emulators configuration', 'pass');
  } else {
    logTest('Firebase emulators configuration', 'warn', 'not configured');
  }
} catch (error) {
  logTest('Firebase configuration', 'fail', 'firebase.json not found or invalid');
}

// Test 2: Check Mobile App Configuration
console.log('\n📱 Checking Mobile App Configuration...');

// Android
const androidConfigPath = 'mobile_app/android/app/google-services.json';
try {
  const androidConfig = JSON.parse(fs.readFileSync(androidConfigPath, 'utf8'));
  if (androidConfig.project_info && androidConfig.project_info.project_id === 'mcq-quiz-system') {
    logTest('Android google-services.json', 'pass', 'valid configuration');
  } else {
    logTest('Android google-services.json', 'fail', 'invalid project ID');
  }
  
  // Check package name
  const client = androidConfig.client && androidConfig.client[0];
  if (client && client.client_info && client.client_info.android_client_info) {
    const packageName = client.client_info.android_client_info.package_name;
    if (packageName === 'com.mcqquiz.app') {
      logTest('Android package name', 'pass', packageName);
    } else {
      logTest('Android package name', 'warn', `found: ${packageName}, expected: com.mcqquiz.app`);
    }
  }
} catch (error) {
  logTest('Android google-services.json', 'fail', 'file not found or invalid JSON');
}

// iOS
const iosConfigPath = 'mobile_app/ios/Runner/GoogleService-Info.plist';
if (fs.existsSync(iosConfigPath)) {
  logTest('iOS GoogleService-Info.plist', 'pass', 'file exists');
} else {
  logTest('iOS GoogleService-Info.plist', 'warn', 'file not found (use template)');
}

// Test 3: Check Web Admin Configuration
console.log('\n🌐 Checking Web Admin Configuration...');

const webConfigPath = 'web_admin/.env.local';
try {
  const webConfig = fs.readFileSync(webConfigPath, 'utf8');
  
  if (webConfig.includes('REACT_APP_FIREBASE_PROJECT_ID=mcq-quiz-system')) {
    logTest('Web Firebase project ID', 'pass');
  } else {
    logTest('Web Firebase project ID', 'fail', 'incorrect or missing');
  }
  
  if (webConfig.includes('REACT_APP_FIREBASE_API_KEY=') && !webConfig.includes('your-web-api-key-here')) {
    logTest('Web Firebase API key', 'pass', 'configured');
  } else {
    logTest('Web Firebase API key', 'fail', 'missing or using template value');
  }
  
} catch (error) {
  logTest('Web .env.local', 'fail', 'file not found');
}

// Test 4: Check Dependencies
console.log('\n📦 Checking Dependencies...');

// Mobile dependencies
try {
  const pubspec = fs.readFileSync('mobile_app/pubspec.yaml', 'utf8');
  if (pubspec.includes('firebase_core:')) {
    logTest('Mobile Firebase Core', 'pass');
  } else {
    logTest('Mobile Firebase Core', 'fail', 'dependency missing');
  }
  
  if (pubspec.includes('firebase_auth:')) {
    logTest('Mobile Firebase Auth', 'pass');
  } else {
    logTest('Mobile Firebase Auth', 'warn', 'dependency missing');
  }
  
  if (pubspec.includes('cloud_firestore:')) {
    logTest('Mobile Firestore', 'pass');
  } else {
    logTest('Mobile Firestore', 'warn', 'dependency missing');
  }
} catch (error) {
  logTest('Mobile pubspec.yaml', 'fail', 'file not found');
}

// Web dependencies
try {
  const packageJson = JSON.parse(fs.readFileSync('web_admin/package.json', 'utf8'));
  if (packageJson.dependencies && packageJson.dependencies.firebase) {
    logTest('Web Firebase SDK', 'pass', `v${packageJson.dependencies.firebase}`);
  } else {
    logTest('Web Firebase SDK', 'fail', 'dependency missing');
  }
} catch (error) {
  logTest('Web package.json', 'fail', 'file not found');
}

// Summary
console.log('\n📊 Test Summary');
console.log('===============');
console.log(`✅ Passed: ${tests.passed}`);
console.log(`⚠️  Warnings: ${tests.warnings}`);
console.log(`❌ Failed: ${tests.failed}`);

if (tests.failed === 0 && tests.warnings === 0) {
  console.log('\n🎉 All tests passed! Firebase is properly configured.');
} else if (tests.failed === 0) {
  console.log('\n✅ Configuration is functional with some warnings.');
} else {
  console.log('\n❌ Configuration has issues that need to be resolved.');
}

console.log('\n🚀 Next Steps:');
console.log('1. Install Firebase CLI: npm install -g firebase-tools');
console.log('2. Login to Firebase: firebase login');
console.log('3. Test mobile app: cd mobile_app && flutter run');
console.log('4. Test web admin: cd web_admin && npm start');
console.log('5. Start emulators: cd firebase && firebase emulators:start');
