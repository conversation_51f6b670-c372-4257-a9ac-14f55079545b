#!/bin/bash

# Firebase Setup Verification Script
# This script helps verify that Firebase is properly configured for the MCQ Quiz app

echo "🔍 Firebase Setup Verification"
echo "=============================="

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed."
    echo "Please install it with: npm install -g firebase-tools"
    exit 1
fi

echo "✅ Firebase CLI found"

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase."
    echo "Please run: firebase login"
    exit 1
fi

echo "✅ Firebase authentication verified"

# Show current project
current_project=$(firebase use --project)
echo "🎯 Current Firebase project: $current_project"

# Verify project ID
if [[ "$current_project" != *"mcq-quiz-system"* ]]; then
    echo "⚠️  WARNING: Project ID might be incorrect"
    echo "Expected: mcq-quiz-system"
    echo "Current: $current_project"
    echo ""
    read -p "Continue anyway? (y/N): " continue_anyway
    if [[ $continue_anyway != [yY] ]]; then
        echo "❌ Verification cancelled"
        exit 1
    fi
fi

echo ""
echo "📋 Checking Firebase configuration..."

# Check if required files exist
files_to_check=(
    "firebase/firestore.rules"
    "firebase/firestore.indexes.json"
    "mobile_app/lib/core/config/firebase_options.dart"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ $file missing"
    fi
done

echo ""
echo "🔥 Firebase Services Status:"

# Check Firestore rules
echo "📜 Checking Firestore rules..."
if firebase firestore:rules:get &> /dev/null; then
    echo "✅ Firestore rules are deployed"
else
    echo "❌ Firestore rules not deployed or accessible"
fi

# Check indexes
echo "📊 Checking Firestore indexes..."
echo "   (Note: This requires manual verification in Firebase Console)"
echo "   Go to: https://console.firebase.google.com/project/$current_project/firestore/indexes"

echo ""
echo "🧪 Testing Firebase connectivity..."

# Test basic Firebase connection
if firebase projects:list | grep -q "$current_project"; then
    echo "✅ Can access Firebase project"
else
    echo "❌ Cannot access Firebase project"
fi

echo ""
echo "📱 Flutter App Configuration:"

# Check Firebase options file
if [ -f "mobile_app/lib/core/config/firebase_options.dart" ]; then
    echo "✅ Firebase options file exists"
    
    # Check if project ID matches
    if grep -q "mcq-quiz-system" "mobile_app/lib/core/config/firebase_options.dart"; then
        echo "✅ Project ID matches in Firebase options"
    else
        echo "⚠️  Project ID might not match in Firebase options"
    fi
else
    echo "❌ Firebase options file missing"
    echo "   Run: flutterfire configure"
fi

echo ""
echo "🎯 Next Steps to Complete Setup:"
echo ""

# Check if the index error was mentioned
echo "1. 📊 CREATE REQUIRED INDEXES:"
echo "   The error you encountered means indexes are needed."
echo "   Options:"
echo "   a) Use the auto-generated link from your error message"
echo "   b) Run: ./deploy-firebase-indexes.sh"
echo "   c) Manual creation in Firebase Console"
echo ""

echo "2. 🔐 VERIFY FIREBASE RULES:"
echo "   If you still get permission errors:"
echo "   a) Run: ./deploy-firebase-rules.sh"
echo "   b) Choose option 2 (Simplified rules) for debugging"
echo ""

echo "3. 🧪 TEST YOUR APP:"
echo "   a) Open your Flutter app"
echo "   b) Go to Settings → Firebase Debug"
echo "   c) Click 'Full Diagnostics'"
echo "   d) Check console output for any remaining issues"
echo ""

echo "4. ✅ VERIFY EXAM DATA LOADING:"
echo "   a) Try to load quizzes in your app"
echo "   b) Should see exam data without errors"
echo "   c) Use debug tools to confirm successful data fetch"
echo ""

echo "🔍 Current Status Summary:"
echo "========================="
echo "✅ Firebase CLI: Working"
echo "✅ Authentication: Working (based on your index error)"
echo "✅ Project Connection: Working"
echo "⏳ Indexes: Need to be created"
echo "⏳ Rules: May need to be updated"
echo ""

echo "📞 If you need help:"
echo "- Check the comprehensive guides created:"
echo "  * FIRESTORE_INDEX_SETUP_GUIDE.md"
echo "  * FIREBASE_RULES_DEPLOYMENT_GUIDE.md"
echo "  * DEBUG_TOOLS_USAGE_GUIDE.md"
echo ""

echo "🎉 The index error you got is actually GOOD NEWS!"
echo "   It means Firebase auth and rules are working."
echo "   You just need to create the database indexes."
echo ""

echo "💡 Quick fix: Click the index creation link from your error message!"
echo "   Or run: ./deploy-firebase-indexes.sh"
