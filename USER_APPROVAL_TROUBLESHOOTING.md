# 🔍 User Approval Troubleshooting Guide

## ❌ Problem: No Users Appearing for Approval

If you're seeing "No users found" in the User Management interface, here's how to diagnose and fix the issue.

## 🧪 Step-by-Step Troubleshooting

### **Step 1: Check What's in Firestore**
```bash
# Run the diagnostic script
cd web_admin
node scripts/check-firestore-users.js
```

This will show you:
- ✅ How many users exist in Firestore
- ✅ Their status (pending/approved/rejected)
- ✅ Who are system admins
- ✅ What needs to be done next

### **Step 2: Check Browser Console**
1. **Open browser console** (F12 → Console)
2. **Navigate to User Management**
3. **Look for debug messages**:
   ```
   🔐 Current admin user: {role: "system_admin", email: "<EMAIL>"}
   ✅ System admin detected, fetching users...
   🔍 Fetching users from admin_users collection...
   📊 Total documents found: X
   👤 User found: {email: "<EMAIL>", status: "pending"}
   ```

### **Step 3: Verify System Admin Role**
Check if you're logged in as a system admin:
```bash
# In browser console, you should see:
🔐 Current admin user: {role: "system_admin", ...}

# If you see role: "admin" or "super_admin", you need system_admin role
```

## 🔧 Common Issues & Solutions

### **Issue 1: No Users Registered Yet**
**Symptoms**: Firestore check shows 0 documents
**Solution**:
```bash
# 1. Register a test user
http://localhost:3000/register

# 2. Fill out the form and submit
# 3. User should appear as pending
```

### **Issue 2: Not Logged in as System Admin**
**Symptoms**: Console shows `role: "admin"` or `role: "super_admin"`
**Solution**:
```bash
# Option A: Create new system admin
node scripts/create-system-admin.js <EMAIL> admin123 "System Administrator"

# Option B: Promote existing user
node scripts/promote-to-system-admin.js <EMAIL>

# Then login with system admin credentials
```

### **Issue 3: Firestore Rules Blocking Access**
**Symptoms**: Console shows permission denied errors
**Solution**:
```bash
# Deploy development rules (more permissive)
./scripts/deploy-firestore-rules.sh dev

# Or manually in Firebase Console:
# 1. Go to Firestore Database → Rules
# 2. Replace with development rules
# 3. Publish
```

### **Issue 4: Firebase Configuration Issues**
**Symptoms**: Network errors or connection failures
**Solution**:
```bash
# 1. Check Firebase project is active
# 2. Verify .env.local has correct config
# 3. Test Firebase connection
http://localhost:3000/test-firebase
```

## 🎯 Quick Fixes

### **Quick Fix 1: Complete Setup from Scratch**
```bash
# 1. Create system admin
node scripts/create-system-admin.js <EMAIL> admin123 "System Administrator"

# 2. Deploy development rules
./scripts/deploy-firestore-rules.sh dev

# 3. Register test user
# Go to: http://localhost:3000/register
# Register with: <EMAIL> / test123 / "Test User"

# 4. Login as system admin
# Go to: http://localhost:3000/login
# Login with: <EMAIL> / admin123

# 5. Check User Management
# Should see test user pending approval
```

### **Quick Fix 2: Reset and Test**
```bash
# 1. Clear browser cache (Ctrl+Shift+R)
# 2. Check Firestore users
node scripts/check-firestore-users.js

# 3. If no users, register one
# 4. If no system admin, create one
# 5. Login and check User Management
```

## 🔍 Debugging Checklist

### **Browser Console Checks**
- [ ] No JavaScript errors
- [ ] Firebase initialized successfully
- [ ] User role shows as "system_admin"
- [ ] Firestore fetch shows documents found
- [ ] No permission denied errors

### **Firestore Checks**
- [ ] admin_users collection exists
- [ ] Documents have correct structure
- [ ] At least one system_admin exists
- [ ] Pending users exist for approval
- [ ] Rules allow system_admin to read all users

### **Authentication Checks**
- [ ] Logged in successfully
- [ ] User appears in header with system_admin badge
- [ ] System Administration section visible on dashboard
- [ ] User Management card clickable

## 📊 Expected Data Flow

### **1. User Registration**
```
User registers → Document created in admin_users → Status: "pending"
```

### **2. System Admin Login**
```
System admin logs in → Fetches all admin_users → Shows pending users
```

### **3. User Approval**
```
System admin clicks approve → User status: "approved" → User can login
```

## 🛠️ Manual Verification

### **Check Firestore Console**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select mcq-quiz-system project
3. Go to Firestore Database
4. Check admin_users collection
5. Verify documents exist with correct structure

### **Check User Document Structure**
```javascript
// Expected pending user document
{
  uid: "firebase_user_id",
  email: "<EMAIL>",
  name: "User Name",
  role: "admin",
  createdAt: timestamp,
  isActive: false,
  status: "pending"
}

// Expected system admin document  
{
  uid: "firebase_user_id",
  email: "<EMAIL>",
  name: "System Administrator", 
  role: "system_admin",
  createdAt: timestamp,
  isActive: true,
  status: "approved"
}
```

## 🎯 Success Indicators

When everything is working correctly, you should see:

### **In User Management Interface**
- ✅ Table with user list
- ✅ Pending users with yellow "Pending Approval" status
- ✅ Approve/Reject buttons for pending users
- ✅ User details (name, email, creation date)

### **In Browser Console**
```
🔐 Current admin user: {role: "system_admin", email: "<EMAIL>"}
✅ System admin detected, fetching users...
🔍 Fetching users from admin_users collection...
📊 Total documents found: 2
👤 User found: {email: "<EMAIL>", status: "pending"}
👤 User found: {email: "<EMAIL>", status: "approved"}
📋 Final users list: 2 users
```

## 📞 Still Having Issues?

If users still don't appear after following this guide:

1. **Run the diagnostic script**: `node scripts/check-firestore-users.js`
2. **Check browser console** for specific error messages
3. **Verify Firebase project** is active and accessible
4. **Test with fresh browser session** (incognito mode)
5. **Check network connectivity** to Firebase

The diagnostic script will give you specific guidance based on what it finds in your Firestore database.
