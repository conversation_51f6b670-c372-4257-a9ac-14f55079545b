# 🔐 Authentication System Setup Guide

## 📋 Overview
The web admin now has a complete authentication system with user registration, login, and admin user management using Firebase.

## 🎯 Features Implemented

### ✅ **Authentication Pages**
- **Login Page** (`/login`) - Email/password authentication
- **Registration Page** (`/register`) - New admin account creation
- **Forgot Password** (`/forgot-password`) - Password reset functionality

### ✅ **Admin User Management**
- **Separate Collection**: `admin_users` in Firestore
- **Role-based Access**: `admin` and `super_admin` roles
- **Account Status**: Active/inactive admin accounts
- **Profile Management**: Name, email, role, timestamps

### ✅ **Security Features**
- **Protected Routes**: All admin pages require authentication
- **Firestore Rules**: Secure access to admin collections
- **Role Validation**: Check admin status before access
- **Session Management**: Automatic login/logout handling

## 🗃️ Database Structure

### Admin Users Collection (`admin_users`)
```javascript
{
  uid: "firebase_user_id",
  email: "<EMAIL>",
  name: "Admin Name",
  role: "admin" | "super_admin",
  createdAt: timestamp,
  lastLogin: timestamp,
  isActive: true
}
```

## 🚀 How to Use

### 1. **First Admin Registration**
```bash
# Start the web admin
cd web_admin
npm start

# Navigate to registration
http://localhost:3000/register

# Fill in admin details:
- Full Name
- Email Address
- Password (min 6 characters)
- Confirm Password
- Agree to Terms
```

### 2. **Admin Login**
```bash
# Navigate to login
http://localhost:3000/login

# Enter credentials:
- Email
- Password
```

### 3. **Password Reset**
```bash
# Navigate to forgot password
http://localhost:3000/forgot-password

# Enter email to receive reset link
```

## 🔧 Configuration

### Firebase Console Setup
1. **Enable Authentication**:
   - Go to Firebase Console → Authentication
   - Enable Email/Password provider
   - Add authorized domains (localhost for development)

2. **Firestore Database**:
   - Create database in test mode
   - Deploy security rules from `firebase/firestore.rules`

3. **Security Rules**:
   ```bash
   cd firebase
   firebase deploy --only firestore:rules
   ```

## 🎨 UI Features

### **Login Page**
- Clean, professional design
- Password visibility toggle
- Remember me functionality
- Links to registration and password reset

### **Registration Page**
- Form validation
- Password confirmation
- Terms and conditions checkbox
- Account activation notice

### **Layout Header**
- User avatar with initials
- User name and role display
- Dropdown menu with profile/settings
- Sign out functionality

## 🔒 Security Rules

### **Admin Users Collection**
- Users can only read/write their own document
- Super admins can manage all admin users
- Active status validation

### **Other Collections**
- Admin users can access questions, categories, analytics
- Mobile app users have separate access rules
- Test collection for Firebase connection testing

## 🧪 Testing

### **Test Authentication Flow**
1. **Register New Admin**:
   ```
   http://localhost:3000/register
   ```

2. **Login with Credentials**:
   ```
   http://localhost:3000/login
   ```

3. **Test Protected Routes**:
   ```
   http://localhost:3000/dashboard
   http://localhost:3000/questions
   ```

4. **Test Firebase Connection**:
   ```
   http://localhost:3000/test-firebase
   ```

## 📊 Admin Roles

### **Admin Role**
- Access to all admin features
- Can manage questions, categories, users
- Can view analytics and reports
- Cannot manage other admin users

### **Super Admin Role**
- All admin permissions
- Can manage other admin users
- Can activate/deactivate accounts
- Full system access

## 🔄 User Flow

```
1. User visits admin panel → Redirected to /login
2. New user clicks "Request Account" → /register
3. User fills registration form → Account created in admin_users
4. User receives confirmation → Redirected to /login
5. User logs in → Authenticated and redirected to /dashboard
6. User accesses admin features → Protected by authentication
7. User logs out → Session cleared, redirected to /login
```

## 🛠️ Development

### **Add New Protected Route**
```typescript
<Route path="/new-feature" element={<NewFeaturePage />} />
```

### **Check User Role**
```typescript
const { adminUser } = useAuth();
if (adminUser?.role === 'super_admin') {
  // Super admin only features
}
```

### **Update User Profile**
```typescript
// Update admin user document in Firestore
await setDoc(doc(db, 'admin_users', user.uid), {
  name: newName,
  // other fields
}, { merge: true });
```

## 🎉 Summary

The authentication system is now fully functional with:
- ✅ **Secure login/registration**
- ✅ **Role-based access control**
- ✅ **Protected admin routes**
- ✅ **Firebase integration**
- ✅ **Professional UI/UX**

**Ready to use**: Start the web admin and register your first admin account!
