# Firebase Rules Deployment Guide

## 🚨 Quick Fix for Permission Denied Error

If you're getting `permission-denied` errors when fetching exam data, follow these steps:

### Option 1: Use Deployment Script (Recommended)

```bash
# Run the deployment script
./deploy-firebase-rules.sh

# Choose option 2 (Simplified rules) or option 4 (Temporary open rules)
```

### Option 2: Manual Deployment via Firebase Console

1. **Go to Firebase Console**
   - Open [Firebase Console](https://console.firebase.google.com)
   - Select your project: `mcq-quiz-system`

2. **Navigate to Firestore Rules**
   - Go to **Firestore Database** → **Rules** tab

3. **Replace Current Rules**
   - Copy and paste this temporary rule set:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // TEMPORARY RULES FOR DEBUGGING - VERY PERMISSIVE
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

4. **Publish Rules**
   - Click **Publish** button
   - Wait for deployment confirmation

## 📋 Available Rule Files

### 1. `firebase/firestore.rules.temp` - Temporary Open Rules
**Use for:** Immediate debugging of permission issues
**Security:** Very permissive - allows all authenticated users full access
```javascript
match /{document=**} {
  allow read, write: if request.auth != null;
}
```

### 2. `firebase/firestore.rules.simple` - Simplified Rules
**Use for:** Basic security with debugging capabilities
**Security:** Allows authenticated users to read/write most collections
```javascript
match /exams/{examId} {
  allow read, write: if request.auth != null;
}
```

### 3. `firebase/firestore.rules.production` - Production Rules
**Use for:** Production deployment with proper security
**Security:** Role-based access control with data validation
```javascript
match /exams/{examId} {
  allow read: if request.auth != null && resource.data.isActive == true;
  allow write: if isAnyAdmin();
}
```

### 4. `firebase/firestore.rules` - Current Rules
**Use for:** Current state (may be causing permission issues)

## 🔧 Troubleshooting

### If Deployment Fails

1. **Check Firebase CLI Installation**
   ```bash
   npm install -g firebase-tools
   firebase --version
   ```

2. **Login to Firebase**
   ```bash
   firebase login
   firebase projects:list
   ```

3. **Verify Project**
   ```bash
   firebase use mcq-quiz-system
   ```

4. **Deploy Manually**
   ```bash
   firebase deploy --only firestore:rules
   ```

### If Permission Denied Persists

1. **Use Most Permissive Rules**
   - Deploy `firebase/firestore.rules.temp`
   - This allows all authenticated users full access

2. **Check Authentication**
   - Ensure user is properly logged in
   - Verify authentication token is valid

3. **Use Debug Tools**
   - In your Flutter app: Settings → Firebase Debug
   - Click "Full Diagnostics"
   - Check console output for detailed information

## 🧪 Testing After Deployment

### 1. Use Flutter Debug Tools

In your Flutter app:
1. Go to **Settings** → **Firebase Debug**
2. Click **"Full Diagnostics"**
3. Check console output for:
   ```
   ✅ User authenticated
   ✅ Basic Firestore access
   ✅ Exams collection read
   ```

### 2. Manual Testing

```dart
// Add this test code to your app
void testFirebaseAccess() async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    print('User: ${user?.uid ?? "NOT AUTHENTICATED"}');
    
    final exams = await ExamService.getActiveExams();
    print('✅ SUCCESS: Found ${exams.length} exams');
  } catch (e) {
    print('❌ ERROR: $e');
  }
}
```

## 🔄 Rule Progression Strategy

### Phase 1: Debug (Use Temporary Rules)
- Deploy `firebase/firestore.rules.temp`
- Verify app works and can fetch data
- Identify any remaining issues

### Phase 2: Basic Security (Use Simplified Rules)
- Deploy `firebase/firestore.rules.simple`
- Test that authenticated users can access data
- Verify admin functions work

### Phase 3: Production (Use Production Rules)
- Deploy `firebase/firestore.rules.production`
- Test role-based access control
- Verify data validation works

## 📊 Monitoring

After deployment, monitor your rules:

1. **Firebase Console**
   - Go to Firestore Database → Usage
   - Check for denied requests

2. **Debug Output**
   - Use Flutter debug tools regularly
   - Monitor console logs for errors

3. **User Feedback**
   - Watch for permission-related user complaints
   - Test with different user roles

## 🚨 Security Notes

- **Temporary rules** (`firestore.rules.temp`) are very permissive - use only for debugging
- **Always test** rules before deploying to production
- **Monitor usage** to detect unauthorized access attempts
- **Implement proper authentication** before using production rules

## 📞 Support

If you continue to have issues:

1. **Check the comprehensive debug output** from the Flutter debug tools
2. **Verify your Firebase project configuration**
3. **Ensure your authentication flow is working correctly**
4. **Test with the most permissive rules first**

The debug tools will provide detailed information about what's failing and why.
