# 🔥 Firebase Setup Guide for MCQ Quiz System

## 📋 Overview
This guide will help you connect Firebase to both the Flutter mobile app and React web admin.

## ⚡ Quick Start (5 minutes)

### 1. Run Setup Script
```bash
chmod +x scripts/setup-firebase.sh
./scripts/setup-firebase.sh
```

### 2. Add Apps in Firebase Console
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select project: **mcq-quiz-system**
3. Add these apps:

**Android App:**
- Package name: `com.mcqquiz.app`
- Download `google-services.json` → `mobile_app/android/app/`

**iOS App:**
- Bundle ID: `com.mcqquiz.mcqQuizApp`
- Download `GoogleService-Info.plist` → `mobile_app/ios/Runner/`

**Web App:**
- Copy config → `web_admin/.env.local`

### 3. Enable Services
- Authentication → Email/Password
- Firestore Database → Test mode
- Storage → Test mode

## 🚀 Step 1: Firebase Console Setup

### 1.1 Create/Access Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select existing project: **mcq-quiz-system** (or create new one)
3. Note your Project ID: `mcq-quiz-system`

### 1.2 Enable Firebase Services
Enable these services in Firebase Console:

#### Authentication
- Go to **Authentication** → **Sign-in method**
- Enable **Email/Password** (for web admin)
- Enable **Phone** (for mobile app - if needed)
- Add authorized domains: `localhost`, your domain

#### Firestore Database
- Go to **Firestore Database** → **Create database**
- Start in **test mode** (for development)
- Choose location closest to your users

#### Storage
- Go to **Storage** → **Get started**
- Start in **test mode**

## 📱 Step 2: Mobile App (Flutter) Configuration

### 2.1 Add Android App
1. In Firebase Console → **Project Settings** → **Your apps**
2. Click **Add app** → **Android**
3. **Android package name**: `com.mcqquiz.app`
4. **App nickname**: MCQ Quiz Mobile
5. Download `google-services.json`
6. Place in: `mobile_app/android/app/google-services.json`

### 2.2 Add iOS App
1. In Firebase Console → **Project Settings** → **Your apps**
2. Click **Add app** → **iOS**
3. **iOS bundle ID**: `com.mcqquiz.mcqQuizApp`
4. **App nickname**: MCQ Quiz iOS
5. Download `GoogleService-Info.plist`
6. Place in: `mobile_app/ios/Runner/GoogleService-Info.plist`

### 2.3 Flutter Dependencies
Your `pubspec.yaml` already has Firebase dependencies. Run:
```bash
cd mobile_app
flutter pub get
```

## 🌐 Step 3: Web Admin Configuration

### 3.1 Add Web App
1. In Firebase Console → **Project Settings** → **Your apps**
2. Click **Add app** → **Web**
3. **App nickname**: MCQ Quiz Admin
4. **Enable Firebase Hosting**: Yes
5. Copy the Firebase config object

### 3.2 Update Web Environment
Create `web_admin/.env.local`:
```env
# Firebase Configuration
REACT_APP_FIREBASE_API_KEY=your-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=mcq-quiz-system.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=mcq-quiz-system
REACT_APP_FIREBASE_STORAGE_BUCKET=mcq-quiz-system.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
REACT_APP_FIREBASE_APP_ID=your-app-id
REACT_APP_FIREBASE_MEASUREMENT_ID=your-measurement-id
```

## 🔧 Step 4: Firebase CLI Setup

### 4.1 Install Firebase CLI
```bash
npm install -g firebase-tools
```

### 4.2 Login and Initialize
```bash
firebase login
cd firebase
firebase use mcq-quiz-system
```

## 🧪 Step 5: Testing

### 5.1 Test Mobile App
```bash
cd mobile_app
flutter run
```

### 5.2 Test Web Admin
```bash
cd web_admin
npm start
```

### 5.3 Test Firebase Functions (Optional)
```bash
cd firebase
firebase emulators:start
```

## 📁 Expected File Structure
```
mcq/
├── mobile_app/
│   ├── android/app/google-services.json ✅
│   └── ios/Runner/GoogleService-Info.plist ✅
├── web_admin/
│   └── .env.local ✅
└── firebase/
    ├── firebase.json ✅
    └── .firebaserc ✅
```

## 🔍 Verification Checklist
- [ ] Firebase project created/selected
- [ ] Authentication enabled
- [ ] Firestore database created
- [ ] Android app added with google-services.json
- [ ] iOS app added with GoogleService-Info.plist
- [ ] Web app added with config in .env.local
- [ ] Firebase CLI installed and logged in
- [ ] Mobile app builds without errors
- [ ] Web admin starts without errors

## 🆘 Troubleshooting
- **Build errors**: Check package names match Firebase console
- **Auth errors**: Verify domains are authorized
- **Network errors**: Check Firebase config values
- **iOS build issues**: Ensure GoogleService-Info.plist is added to Xcode project

## 📞 Next Steps
After setup:
1. Configure Firestore security rules
2. Set up authentication flows
3. Test data operations
4. Deploy to production
