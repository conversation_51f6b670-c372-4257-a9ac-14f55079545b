#!/bin/bash

# Deploy Firebase Indexes Script
# This script deploys the required Firestore indexes for the MCQ Quiz app

echo "📊 Firebase Indexes Deployment Script"
echo "====================================="

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed."
    echo "Please install it with: npm install -g firebase-tools"
    exit 1
fi

echo "✅ Firebase CLI found"

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase."
    echo "Please run: firebase login"
    exit 1
fi

echo "✅ Firebase authentication verified"

# Show current project
current_project=$(firebase use --project)
echo "🎯 Current Firebase project: $current_project"

# Check if indexes file exists
if [ ! -f "firebase/firestore.indexes.json" ]; then
    echo "❌ Indexes file not found: firebase/firestore.indexes.json"
    exit 1
fi

echo "✅ Indexes file found: firebase/firestore.indexes.json"

# Show what indexes will be deployed
echo ""
echo "📋 Indexes to be deployed:"
echo "1. exams collection:"
echo "   - isActive (ASC) + createdAt (DESC)"
echo "   - isActive (ASC) + suitableFor (CONTAINS) + createdAt (DESC)"
echo "2. questions collection (existing)"
echo "3. quiz_sessions collection (existing)"
echo "4. quiz_results collection (existing)"
echo "5. leaderboard collection (existing)"
echo "6. users collection (existing)"

# Confirm deployment
echo ""
echo "⚠️  You are about to deploy Firestore indexes to: $current_project"
echo ""
read -p "Are you sure you want to continue? (y/N): " confirm

if [[ $confirm != [yY] && $confirm != [yY][eE][sS] ]]; then
    echo "❌ Deployment cancelled"
    exit 1
fi

# Deploy indexes
echo "🚀 Deploying indexes..."
if firebase deploy --only firestore:indexes; then
    echo ""
    echo "✅ Indexes deployed successfully!"
    echo ""
    echo "⏳ Index creation status:"
    echo "- Indexes are being built in the background"
    echo "- This can take several minutes for large collections"
    echo "- You can monitor progress in Firebase Console"
    echo ""
    echo "🔍 Next steps:"
    echo "1. Wait for index creation to complete (check Firebase Console)"
    echo "2. Test your Flutter app - the query should now work"
    echo "3. Use debug tools to verify: Settings → Firebase Debug → Full Diagnostics"
    echo ""
    echo "📊 Monitor index creation:"
    echo "https://console.firebase.google.com/project/$current_project/firestore/indexes"
    echo ""
else
    echo ""
    echo "❌ Index deployment failed!"
    echo ""
    echo "🔧 Possible solutions:"
    echo "1. Check if you have permission to deploy to this project"
    echo "2. Verify you're logged in with the correct account: firebase login"
    echo "3. Check if the project ID is correct: firebase use --project"
    echo "4. Try creating indexes manually via Firebase Console"
    echo ""
    echo "📖 Manual index creation:"
    echo "1. Go to https://console.firebase.google.com"
    echo "2. Select your project: $current_project"
    echo "3. Go to Firestore Database → Indexes"
    echo "4. Create composite index for 'exams' collection:"
    echo "   - Field 1: isActive (Ascending)"
    echo "   - Field 2: createdAt (Descending)"
    echo ""
    exit 1
fi

echo ""
echo "🎉 Deployment complete!"
echo ""
echo "⚠️  Important notes:"
echo "- Index creation happens asynchronously"
echo "- Large collections may take time to index"
echo "- Your app queries will work once indexing is complete"
echo "- Monitor progress in Firebase Console → Firestore → Indexes"
