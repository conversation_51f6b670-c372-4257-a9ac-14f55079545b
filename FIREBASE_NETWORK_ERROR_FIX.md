# 🔧 Firebase Network Error Fix Guide

## ❌ Error Description
```
Firebase: Error (auth/network-request-failed)
```

## 🔍 Root Causes & Solutions

### **1. Firebase Authentication Not Enabled**

#### **Problem**: Authentication service is not enabled in Firebase Console
#### **Solution**:
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select project: **mcq-quiz-system**
3. Navigate to **Authentication** → **Sign-in method**
4. **Enable Email/Password** provider
5. Click **Save**

### **2. Domain Not Authorized**

#### **Problem**: localhost is not in authorized domains
#### **Solution**:
1. In Firebase Console → **Authentication** → **Settings**
2. Scroll to **Authorized domains**
3. Add these domains:
   - `localhost`
   - `127.0.0.1`
   - Your production domain (if any)

### **3. Firebase Project Configuration**

#### **Problem**: Incorrect project configuration
#### **Solution**: Verify your Firebase config in `.env.local`:

```env
# Verify these values match your Firebase project
REACT_APP_FIREBASE_API_KEY=AIzaSyDIdFTL8Xl-E02bYB_HnuymfGBRRL6xBqk
REACT_APP_FIREBASE_AUTH_DOMAIN=mcq-quiz-system.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=mcq-quiz-system
REACT_APP_FIREBASE_STORAGE_BUCKET=mcq-quiz-system.firebasestorage.app
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=109048215498
REACT_APP_FIREBASE_APP_ID=1:109048215498:web:398b38704a2b075fb08133
```

### **4. Network Connectivity Issues**

#### **Problem**: Firewall or network blocking Firebase
#### **Solution**:
- Check internet connection
- Disable VPN temporarily
- Check corporate firewall settings
- Try different network

## 🚀 Quick Fix Steps

### **Step 1: Enable Firebase Authentication**
```bash
# 1. Go to Firebase Console
# 2. Select mcq-quiz-system project
# 3. Authentication → Sign-in method
# 4. Enable Email/Password
# 5. Add localhost to authorized domains
```

### **Step 2: Restart Development Server**
```bash
cd web_admin
# Stop current server (Ctrl+C)
npm start
```

### **Step 3: Test Authentication**
```bash
# Navigate to: http://localhost:3000/login
# Try to register a new account
# Check browser console for errors
```

## 🧪 Testing Firebase Connection

### **Option 1: Use Firebase Test Page**
```bash
# Navigate to: http://localhost:3000/test-firebase
# Click "Run Firebase Tests"
# Check connection status
```

### **Option 2: Browser Console Check**
```javascript
// Open browser console (F12)
// Check for Firebase initialization logs:
// "🔥 Using production Firebase services"
// "Firebase initialized for environment: development"
```

## 🔧 Alternative Solutions

### **Solution A: Use Firebase Emulators**
If you want to use local Firebase emulators:

```bash
# 1. Update .env.local
REACT_APP_USE_EMULATORS=true

# 2. Start Firebase emulators
cd firebase
firebase emulators:start

# 3. Start web admin in another terminal
cd web_admin
npm start
```

### **Solution B: Reset Firebase Configuration**
```bash
# 1. Delete current config
rm web_admin/.env.local

# 2. Copy template
cp web_admin/.env.local.template web_admin/.env.local

# 3. Update with correct Firebase config values
# 4. Restart development server
```

### **Solution C: Check Firebase Project Status**
```bash
# Verify project exists and is active
firebase projects:list

# Check current project
firebase use

# Switch to correct project if needed
firebase use mcq-quiz-system
```

## 📋 Verification Checklist

- [ ] Firebase project exists and is active
- [ ] Authentication is enabled in Firebase Console
- [ ] Email/Password provider is enabled
- [ ] localhost is in authorized domains
- [ ] `.env.local` has correct Firebase config
- [ ] Development server restarted after config changes
- [ ] Internet connection is working
- [ ] No VPN or firewall blocking Firebase

## 🎯 Expected Results After Fix

✅ **Login page loads without errors**
✅ **Registration form works**
✅ **Firebase test page shows all green**
✅ **Console shows Firebase initialization logs**
✅ **Authentication flow works end-to-end**

## 🆘 Still Having Issues?

### **Debug Steps**:
1. **Check Browser Console** (F12) for detailed error messages
2. **Check Network Tab** to see failed requests
3. **Verify Firebase Console** project settings
4. **Test with different browser** or incognito mode
5. **Check Firebase Status** at [status.firebase.google.com](https://status.firebase.google.com)

### **Common Error Messages**:
- `auth/network-request-failed` → Enable Authentication + check domains
- `auth/api-key-not-valid` → Check API key in .env.local
- `auth/project-not-found` → Verify project ID
- `auth/app-not-authorized` → Add domain to authorized list

## 📞 Quick Resolution

**Most Common Fix** (90% of cases):
1. Go to Firebase Console
2. Enable Authentication → Email/Password
3. Add `localhost` to authorized domains
4. Restart development server
5. Test login/registration

This should resolve the network error and get authentication working! 🎉
