import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mcq_quiz_app/core/providers/auth_provider_minimal.dart';

void main() {
  group('Registration Process Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should send registration OTP successfully', () async {
      final authNotifier = container.read(authProvider.notifier);

      // Test phone number registration
      final result = await authNotifier.registerWithPhone(
        phoneNumber: '+************',
        name: 'Test User',
        email: '<EMAIL>',
        officeName: 'Test Office',
        designation: 'GDS',
      );

      expect(result, isTrue);

      // Check that loading state is handled correctly
      final authState = container.read(authProvider);
      expect(authState.isLoading, isFalse);
      expect(authState.error, isNull);
    });

    test('should handle registration OTP verification', () async {
      final authNotifier = container.read(authProvider.notifier);

      // First send OTP
      await authNotifier.registerWithPhone(
        phoneNumber: '+************',
        name: 'Test User',
        email: '<EMAIL>',
        officeName: 'Test Office',
        designation: 'GDS',
      );

      // Then verify OTP (this will fail in test environment but should handle gracefully)
      try {
        await authNotifier.verifyRegistrationOTP(
          '123456',
          name: 'Test User',
          email: '<EMAIL>',
          officeName: 'Test Office',
          designation: 'GDS',
        );
      } catch (e) {
        // Expected to fail in test environment due to Firebase REST API
        expect(e.toString(), contains('Failed to verify registration OTP'));
      }
    });

    test('should handle login OTP flow', () async {
      final authNotifier = container.read(authProvider.notifier);

      // Test login OTP sending
      final result = await authNotifier.sendOTP('+************');

      expect(result, isTrue);

      // Check state
      final authState = container.read(authProvider);
      expect(authState.isLoading, isFalse);
      expect(authState.error, isNull);
    });

    test('should handle OTP verification for login', () async {
      final authNotifier = container.read(authProvider.notifier);

      // First send OTP
      await authNotifier.sendOTP('+************');

      // Then verify OTP (this will fail in test environment but should handle gracefully)
      try {
        await authNotifier.verifyOTP('123456');
      } catch (e) {
        // Expected to fail in test environment due to Firebase REST API
        expect(e.toString(), contains('Failed to verify OTP'));
      }
    });

    test('should validate phone number format', () async {
      final authNotifier = container.read(authProvider.notifier);

      // Test with invalid phone number
      try {
        await authNotifier.registerWithPhone(
          phoneNumber: 'invalid',
          name: 'Test User',
          email: '<EMAIL>',
          officeName: 'Test Office',
          designation: 'GDS',
        );
      } catch (e) {
        // Should handle invalid phone number gracefully
        expect(e.toString(), contains('Failed to send registration OTP'));
      }
    });

    test('should handle user model correctly', () {
      // Test AppUser model
      final user = AppUser(
        uid: 'test_uid',
        email: '<EMAIL>',
        phoneNumber: '+************',
        displayName: 'Test User',
        isEmailVerified: false,
        isPhoneVerified: true,
        lastSignInTime: DateTime.now(),
        registrationDate: DateTime.now(),
      );

      expect(user.isPhoneUser, isTrue);
      expect(user.primaryIdentifier, equals('+************'));
      expect(user.isPhoneVerified, isTrue);
    });

    test('should handle user model without phone number', () {
      // Test AppUser model without phone
      final user = AppUser(
        uid: 'test_uid',
        email: '<EMAIL>',
        displayName: 'Test User',
        isEmailVerified: true,
        lastSignInTime: DateTime.now(),
      );

      expect(user.isPhoneUser, isFalse);
      expect(user.primaryIdentifier, equals('<EMAIL>'));
      expect(user.isPhoneVerified, isFalse);
    });
  });
}
