import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/onboarding/screens/onboarding_screen.dart';
import '../../features/auth/screens/phone_login_screen.dart';
import '../../features/auth/screens/registration_screen.dart';
import '../../features/auth/screens/otp_verification_screen.dart';
import '../../features/auth/screens/registration_otp_screen.dart';
import '../../features/home/<USER>/home_screen.dart';
import '../../features/quiz/screens/quiz_list_screen.dart';
import '../../features/quiz/screens/quiz_screen.dart';
import '../../features/quiz/screens/quiz_result_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/settings/screens/settings_screen.dart';
import '../../features/search/screens/search_screen.dart';

/// App router provider
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/auth/login',
    routes: [
      // Authentication routes
      GoRoute(
        path: '/auth/login',
        name: 'phone-login',
        builder: (context, state) => const PhoneLoginScreen(),
      ),
      GoRoute(
        path: '/auth/register',
        name: 'registration',
        builder: (context, state) => const RegistrationScreen(),
      ),
      GoRoute(
        path: '/auth/otp',
        name: 'otp-verification',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          return OtpVerificationScreen(
            phoneNumber: extra?['phoneNumber'] ?? '',
            countryCode: extra?['countryCode'] ?? '+91',
            countryFlag: extra?['countryFlag'] ?? '🇮🇳',
          );
        },
      ),
      GoRoute(
        path: '/auth/registration-otp',
        name: 'registration-otp-verification',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          return RegistrationOtpScreen(
            phoneNumber: extra?['phoneNumber'] ?? '',
            countryCode: extra?['countryCode'] ?? '+91',
            name: extra?['name'] ?? '',
            email: extra?['email'] ?? '',
            officeName: extra?['officeName'] ?? '',
            designation: extra?['designation'] ?? '',
          );
        },
      ),

      // Onboarding route
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),

      // Main app routes
      ShellRoute(
        builder: (context, state, child) => MainShell(child: child),
        routes: [
          // Home route
          GoRoute(
            path: '/home',
            name: 'home',
            pageBuilder: (context, state) => _buildPageWithTransition(
              context,
              state,
              const HomeScreen(),
            ),
          ),

          // Quiz routes
          GoRoute(
            path: '/quiz',
            name: 'quiz-list',
            pageBuilder: (context, state) => _buildPageWithTransition(
              context,
              state,
              const QuizListScreen(),
            ),
            routes: [
              GoRoute(
                path: ':quizId',
                name: 'quiz',
                builder: (context, state) {
                  final quizId = state.pathParameters['quizId']!;
                  return QuizScreen(quizId: quizId);
                },
                routes: [
                  GoRoute(
                    path: 'result',
                    name: 'quiz-result',
                    builder: (context, state) {
                      final quizId = state.pathParameters['quizId']!;
                      final score = state.extra as int? ?? 0;
                      return QuizResultScreen(
                        quizId: quizId,
                        score: score,
                      );
                    },
                  ),
                ],
              ),
            ],
          ),

          // Search route
          GoRoute(
            path: '/search',
            name: 'search',
            pageBuilder: (context, state) => _buildPageWithTransition(
              context,
              state,
              SearchScreen(
                initialQuery: state.uri.queryParameters['q'],
              ),
            ),
          ),

          // Profile route
          GoRoute(
            path: '/profile',
            name: 'profile',
            pageBuilder: (context, state) => _buildPageWithTransition(
              context,
              state,
              const ProfileScreen(),
            ),
          ),

          // Settings route
          GoRoute(
            path: '/settings',
            name: 'settings',
            pageBuilder: (context, state) => _buildPageWithTransition(
              context,
              state,
              const SettingsScreen(),
            ),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => ErrorScreen(error: state.error),
  );
});

/// Main shell widget for bottom navigation
class MainShell extends StatelessWidget {
  final Widget child;

  const MainShell({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: const BottomNavBar(),
    );
  }
}

/// Bottom navigation bar
class BottomNavBar extends ConsumerWidget {
  const BottomNavBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLocation = GoRouterState.of(context).matchedLocation;

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: _getCurrentIndex(currentLocation),
      onTap: (index) => _onTap(context, index),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'Home',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.quiz),
          label: 'Quiz',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'Profile',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.settings),
          label: 'Settings',
        ),
      ],
    );
  }

  int _getCurrentIndex(String location) {
    if (location.startsWith('/home')) return 0;
    if (location.startsWith('/quiz')) return 1;
    if (location.startsWith('/profile')) return 2;
    if (location.startsWith('/settings')) return 3;
    return 0;
  }

  void _onTap(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.goNamed('home');
        break;
      case 1:
        context.goNamed('quiz-list');
        break;
      case 2:
        context.goNamed('profile');
        break;
      case 3:
        context.goNamed('settings');
        break;
    }
  }
}

/// Build page with smooth transition
Page<dynamic> _buildPageWithTransition(
  BuildContext context,
  GoRouterState state,
  Widget child,
) {
  return CustomTransitionPage<void>(
    key: state.pageKey,
    child: child,
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      // Fade transition for smooth page changes
      return FadeTransition(
        opacity: CurveTween(curve: Curves.easeInOut).animate(animation),
        child: child,
      );
    },
    transitionDuration: const Duration(milliseconds: 200),
    reverseTransitionDuration: const Duration(milliseconds: 200),
  );
}

/// Error screen for routing errors
class ErrorScreen extends StatelessWidget {
  final Exception? error;

  const ErrorScreen({
    super.key,
    this.error,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Something went wrong!',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error?.toString() ?? 'Unknown error occurred',
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.goNamed('home'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Route names for easy access
class AppRoutes {
  static const String phoneLogin = 'phone-login';
  static const String registration = 'registration';
  static const String otpVerification = 'otp-verification';
  static const String registrationOtpVerification =
      'registration-otp-verification';
  static const String onboarding = 'onboarding';
  static const String home = 'home';
  static const String quizList = 'quiz-list';
  static const String quiz = 'quiz';
  static const String quizResult = 'quiz-result';
  static const String profile = 'profile';
  static const String settings = 'settings';
}

/// Extension for easy navigation
extension GoRouterExtension on BuildContext {
  /// Navigate to phone login
  void goToPhoneLogin() => goNamed(AppRoutes.phoneLogin);

  /// Navigate to registration
  void goToRegistration() => goNamed(AppRoutes.registration);

  /// Navigate to OTP verification
  void goToOtpVerification(Map<String, dynamic> data) => goNamed(
        AppRoutes.otpVerification,
        extra: data,
      );

  /// Navigate to registration OTP verification
  void goToRegistrationOtpVerification(Map<String, dynamic> data) => goNamed(
        AppRoutes.registrationOtpVerification,
        extra: data,
      );

  /// Navigate to onboarding
  void goToOnboarding() => goNamed(AppRoutes.onboarding);

  /// Navigate to home
  void goToHome() => goNamed(AppRoutes.home);

  /// Navigate to quiz list
  void goToQuizList() => goNamed(AppRoutes.quizList);

  /// Navigate to specific quiz
  void goToQuiz(String quizId) => goNamed(
        AppRoutes.quiz,
        pathParameters: {'quizId': quizId},
      );

  /// Navigate to quiz result
  void goToQuizResult(String quizId, int score) => goNamed(
        AppRoutes.quizResult,
        pathParameters: {'quizId': quizId},
        extra: score,
      );

  /// Navigate to profile
  void goToProfile() => goNamed(AppRoutes.profile);

  /// Navigate to settings
  void goToSettings() => goNamed(AppRoutes.settings);
}
