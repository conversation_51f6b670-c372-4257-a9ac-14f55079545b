import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/firebase_auth_service.dart';
import '../services/firebase_realtime_auth_service.dart';
import '../services/firebase_auth_test_service.dart';
import '../services/firestore_service.dart';

/// User model for authentication
class AppUser {
  final String uid;
  final String? email;
  final String? phoneNumber;
  final String? displayName;
  final String? photoURL;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final DateTime? lastSignInTime;
  final DateTime? registrationDate;

  const AppUser({
    required this.uid,
    this.email,
    this.phoneNumber,
    this.displayName,
    this.photoURL,
    required this.isEmailVerified,
    this.isPhoneVerified = false,
    this.lastSignInTime,
    this.registrationDate,
  });

  AppUser copyWith({
    String? uid,
    String? email,
    String? phoneNumber,
    String? displayName,
    String? photoURL,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    DateTime? lastSignInTime,
    DateTime? registrationDate,
  }) {
    return AppUser(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      lastSignInTime: lastSignInTime ?? this.lastSignInTime,
      registrationDate: registrationDate ?? this.registrationDate,
    );
  }

  // Helper method to check if user is registered via phone
  bool get isPhoneUser => phoneNumber != null && phoneNumber!.isNotEmpty;

  // Helper method to get primary identifier
  String get primaryIdentifier => phoneNumber ?? email ?? uid;
}

/// Authentication state
class AuthState {
  final AppUser? user;
  final bool isLoading;
  final String? error;
  final bool isFirstTime;
  final String? pendingNavigationRoute;
  final Map<String, dynamic>? pendingNavigationData;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isFirstTime = true,
    this.pendingNavigationRoute,
    this.pendingNavigationData,
  });

  AuthState copyWith({
    AppUser? user,
    bool? isLoading,
    String? error,
    bool? isFirstTime,
    String? pendingNavigationRoute,
    Map<String, dynamic>? pendingNavigationData,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isFirstTime: isFirstTime ?? this.isFirstTime,
      pendingNavigationRoute:
          pendingNavigationRoute ?? this.pendingNavigationRoute,
      pendingNavigationData:
          pendingNavigationData ?? this.pendingNavigationData,
    );
  }

  bool get isAuthenticated => user != null;
  bool get isGuest => user == null && !isLoading;
}

/// Auth provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>(
  (ref) => AuthNotifier(),
);

/// Current user provider
final currentUserProvider = Provider<AppUser?>((ref) {
  return ref.watch(authProvider).user;
});

/// Authentication status provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

/// Minimal Auth notifier class for iOS build testing
class AuthNotifier extends StateNotifier<AuthState> {
  static const String _firstTimeKey = 'is_first_time';

  // Enable test mode when Firebase billing is not enabled
  static const bool _useTestMode =
      false; // Set to false when Firebase billing is enabled

  AuthNotifier() : super(const AuthState()) {
    _init();
  }

  /// Initialize auth state
  Future<void> _init() async {
    state = state.copyWith(isLoading: true);

    try {
      // Check if first time user
      final prefs = await SharedPreferences.getInstance();
      final isFirstTime = prefs.getBool(_firstTimeKey) ?? true;

      // For now, just set initial state
      state = state.copyWith(
        isLoading: false,
        isFirstTime: isFirstTime,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Mock sign in with email and password
  Future<bool> signInWithEmailPassword(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Mock authentication - replace with REST API calls
      await Future.delayed(const Duration(seconds: 1));

      final user = AppUser(
        uid: 'mock_uid_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        displayName: 'Test User',
        isEmailVerified: true,
        lastSignInTime: DateTime.now(),
      );

      state = state.copyWith(
        user: user,
        isLoading: false,
        error: null,
      );

      await _setNotFirstTime();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Authentication failed',
      );
      return false;
    }
  }

  /// Mock sign up with email and password
  Future<bool> signUpWithEmailPassword(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Mock registration - replace with REST API calls
      await Future.delayed(const Duration(seconds: 1));

      final user = AppUser(
        uid: 'mock_uid_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        displayName: 'New User',
        isEmailVerified: false,
        lastSignInTime: DateTime.now(),
      );

      state = state.copyWith(
        user: user,
        isLoading: false,
        error: null,
      );

      await _setNotFirstTime();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Registration failed',
      );
      return false;
    }
  }

  /// Send OTP for phone authentication (login) using real-time service
  Future<bool> sendOTP(String phoneNumber) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      if (_useTestMode) {
        await FirebaseAuthTestService.sendLoginOTP(phoneNumber);
      } else {
        await FirebaseRealtimeAuthService.sendLoginOTP(
          phoneNumber: phoneNumber,
          onCodeSent: (message) {
            if (kDebugMode) print('DEBUG: Login OTP sent - $message');
            state = state.copyWith(isLoading: false, error: null);
          },
          onVerificationFailed: (error) {
            if (kDebugMode) print('DEBUG: Login verification failed - $error');
            state = state.copyWith(
              isLoading: false,
              error: error,
            );
          },
          onVerificationCompleted: () {
            if (kDebugMode) print('DEBUG: Login auto-verification completed');
            state = state.copyWith(isLoading: false, error: null);
          },
        );
      }
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to send OTP: ${e.toString()}',
      );
      return false;
    }
  }

  /// Verify OTP for login
  Future<bool> verifyOTP(String otp) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      if (_useTestMode) {
        await FirebaseAuthTestService.verifyLoginOTP(otp);

        // Create a test user for login
        final user = AppUser(
          uid: 'test_user_${DateTime.now().millisecondsSinceEpoch}',
          email: '<EMAIL>',
          phoneNumber: '+************',
          displayName: 'Test User',
          isEmailVerified: false,
          isPhoneVerified: true,
          lastSignInTime: DateTime.now(),
        );

        state = state.copyWith(
          user: user,
          isLoading: false,
          error: null,
        );

        await _setNotFirstTime();
        return true;
      } else {
        final firebaseUser =
            await FirebaseRealtimeAuthService.verifyLoginOTP(otp);

        if (firebaseUser != null) {
          // Get user data from Firestore (for future use)
          await FirestoreService.getUserDocument(firebaseUser.uid);

          final user = AppUser(
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            phoneNumber: firebaseUser.phoneNumber,
            displayName: firebaseUser.displayName,
            isEmailVerified: firebaseUser.emailVerified,
            isPhoneVerified: true,
            lastSignInTime: DateTime.now(),
          );

          state = state.copyWith(
            user: user,
            isLoading: false,
            error: null,
          );

          await _setNotFirstTime();
          return true;
        } else {
          throw Exception('User authentication failed');
        }
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to verify OTP: ${e.toString()}',
      );
      return false;
    }
  }

  /// Register with phone number using real-time OTP
  Future<bool> registerWithPhone({
    required String phoneNumber,
    required String name,
    required String email,
    required String officeName,
    required String designation,
  }) async {
    if (kDebugMode) {
      print('DEBUG: AuthNotifier.registerWithPhone called');
      print('DEBUG: Test mode: $_useTestMode');
      print(
          'DEBUG: Current auth state - isAuthenticated: ${state.isAuthenticated}, user: ${state.user?.uid}');
      print(
          'DEBUG: Data - name: $name, email: $email, phone: $phoneNumber, office: $officeName, designation: $designation');
    }

    state = state.copyWith(isLoading: true, error: null);

    if (kDebugMode) {
      print(
          'DEBUG: State after setting loading - isAuthenticated: ${state.isAuthenticated}, user: ${state.user?.uid}');
    }

    try {
      if (_useTestMode) {
        if (kDebugMode) print('DEBUG: Using test mode for registration');
        await FirebaseAuthTestService.sendRegistrationOTP(
          phoneNumber: phoneNumber,
          name: name,
          email: email,
          officeName: officeName,
          designation: designation,
        );
      } else {
        if (kDebugMode)
          print('DEBUG: Using Firebase Real-time Auth for registration');
        await FirebaseRealtimeAuthService.sendRegistrationOTP(
          phoneNumber: phoneNumber,
          name: name,
          email: email,
          officeName: officeName,
          designation: designation,
          onCodeSent: (message) {
            if (kDebugMode) print('DEBUG: OTP Code sent - $message');
            // Update state to indicate OTP was sent successfully
            state = state.copyWith(isLoading: false, error: null);
          },
          onVerificationFailed: (error) {
            if (kDebugMode) print('DEBUG: Verification failed - $error');
            state = state.copyWith(
              isLoading: false,
              error: error,
            );
          },
          onVerificationCompleted: () {
            if (kDebugMode) print('DEBUG: Auto-verification completed');
            state = state.copyWith(isLoading: false, error: null);
          },
          onRegistrationSuccess: (user) {
            if (kDebugMode) {
              print('DEBUG: Registration completed for user: ${user.uid}');
              print('DEBUG: User phone: ${user.phoneNumber}');
              print('DEBUG: User email: ${user.email}');
            }
            // Don't set user in state - registration complete but user should login separately
            state = state.copyWith(isLoading: false, error: null);
          },
        );
      }

      // Reset loading state immediately after registration
      // IMPORTANT: Do NOT set user here - this is just sending OTP, not authenticating
      state = state.copyWith(isLoading: false, user: null);

      if (kDebugMode) {
        print('DEBUG: Registration completed successfully');
        print(
            'DEBUG: Final state - isAuthenticated: ${state.isAuthenticated}, user: ${state.user?.uid}');
      }
      return true;
    } catch (e) {
      if (kDebugMode) print('DEBUG: Registration failed with error: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to send registration OTP: ${e.toString()}',
      );
      return false;
    }
  }

  /// Verify OTP for registration using real-time service
  Future<bool> verifyRegistrationOTP(String otp) async {
    print('DEBUG: verifyRegistrationOTP called');
    print(
        'DEBUG: Current auth state before verification - isAuthenticated: ${state.isAuthenticated}, user: ${state.user?.uid}');

    state = state.copyWith(isLoading: true, error: null);

    try {
      if (_useTestMode) {
        await FirebaseAuthTestService.verifyRegistrationOTP(otp);
        // Registration completed successfully in test mode
        // IMPORTANT: Do NOT set user here - registration is complete but user is not logged in
        state = state.copyWith(
          isLoading: false,
          error: null,
          user: null, // Explicitly ensure no user is set
        );
        await _setNotFirstTime();
        print(
            'DEBUG: Registration OTP verified successfully - user should NOT be authenticated');
        print(
            'DEBUG: Auth state after registration - isAuthenticated: ${state.isAuthenticated}, user: ${state.user?.uid}');
        return true;
      } else {
        final firebaseUser =
            await FirebaseRealtimeAuthService.verifyRegistrationOTP(otp);
        if (firebaseUser != null) {
          // Registration completed successfully but user should not be authenticated yet
          state = state.copyWith(
            isLoading: false,
            error: null,
            user: null, // Explicitly ensure no user is set
          );
          await _setNotFirstTime();
          print(
              'DEBUG: Registration OTP verified successfully - user should NOT be authenticated');
          print(
              'DEBUG: Auth state after registration - isAuthenticated: ${state.isAuthenticated}, user: ${state.user?.uid}');
          return true;
        } else {
          throw Exception('Registration failed');
        }
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to verify registration OTP: ${e.toString()}',
      );
      return false;
    }
  }

  /// Resend registration OTP
  Future<bool> resendRegistrationOTP() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      if (_useTestMode) {
        // For test mode, just simulate success
        state = state.copyWith(isLoading: false, error: null);
        return true;
      } else {
        await FirebaseRealtimeAuthService.resendRegistrationOTP();
        state = state.copyWith(isLoading: false, error: null);
        return true;
      }
    } catch (e) {
      if (kDebugMode) print('DEBUG: Failed to resend registration OTP: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to resend OTP: ${e.toString()}',
      );
      return false;
    }
  }

  /// Mock reset password
  Future<bool> resetPassword(String email) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await Future.delayed(const Duration(seconds: 1));
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to send reset email',
      );
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    state = state.copyWith(isLoading: true);

    try {
      await Future.delayed(const Duration(milliseconds: 500));
      state = state.copyWith(
        user: null,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      debugPrint('Error signing out: $e');
      state = state.copyWith(isLoading: false);
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Set not first time
  Future<void> _setNotFirstTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_firstTimeKey, false);
      state = state.copyWith(isFirstTime: false);
    } catch (e) {
      debugPrint('Error setting first time flag: $e');
    }
  }

  /// Complete onboarding (public method)
  Future<void> completeOnboarding() async {
    await _setNotFirstTime();
  }

  /// Set pending navigation for later use
  void setPendingNavigation(String route, Map<String, dynamic> data) {
    debugPrint('🔥 AUTH PROVIDER: Setting pending navigation to $route');
    state = state.copyWith(
      pendingNavigationRoute: route,
      pendingNavigationData: data,
    );
  }

  /// Clear pending navigation
  void clearPendingNavigation() {
    debugPrint('🔥 AUTH PROVIDER: Clearing pending navigation');
    state = state.copyWith(
      pendingNavigationRoute: null,
      pendingNavigationData: null,
    );
  }
}
