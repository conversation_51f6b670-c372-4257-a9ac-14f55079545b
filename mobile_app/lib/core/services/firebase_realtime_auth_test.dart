import 'package:flutter/foundation.dart';
import 'firebase_realtime_auth_service.dart';

/// Test class for Firebase Real-time Auth Service
class FirebaseRealtimeAuthTest {
  
  /// Test registration OTP flow
  static Future<void> testRegistrationOTP() async {
    if (kDebugMode) {
      print('=== Testing Firebase Real-time Registration OTP ===');
    }
    
    try {
      // Test sending registration OTP
      await FirebaseRealtimeAuthService.sendRegistrationOTP(
        phoneNumber: '+************', // Use a test phone number
        name: 'Test User',
        email: '<EMAIL>',
        officeName: 'Test Office',
        designation: 'GDS',
        onCodeSent: (message) {
          if (kDebugMode) print('✅ Code sent: $message');
        },
        onVerificationFailed: (error) {
          if (kDebugMode) print('❌ Verification failed: $error');
        },
        onVerificationCompleted: () {
          if (kDebugMode) print('✅ Auto-verification completed');
        },
        onRegistrationSuccess: (user) {
          if (kDebugMode) print('✅ Registration success for user: ${user.uid}');
        },
      );
      
      if (kDebugMode) print('✅ Registration OTP sent successfully');
      
    } catch (e) {
      if (kDebugMode) print('❌ Registration OTP test failed: $e');
    }
  }
  
  /// Test login OTP flow
  static Future<void> testLoginOTP() async {
    if (kDebugMode) {
      print('=== Testing Firebase Real-time Login OTP ===');
    }
    
    try {
      // Test sending login OTP
      await FirebaseRealtimeAuthService.sendLoginOTP(
        phoneNumber: '+************', // Use a test phone number
        onCodeSent: (message) {
          if (kDebugMode) print('✅ Login code sent: $message');
        },
        onVerificationFailed: (error) {
          if (kDebugMode) print('❌ Login verification failed: $error');
        },
        onVerificationCompleted: () {
          if (kDebugMode) print('✅ Login auto-verification completed');
        },
      );
      
      if (kDebugMode) print('✅ Login OTP sent successfully');
      
    } catch (e) {
      if (kDebugMode) print('❌ Login OTP test failed: $e');
    }
  }
  
  /// Test OTP verification (requires manual OTP input)
  static Future<void> testOTPVerification(String otp, {bool isRegistration = true}) async {
    if (kDebugMode) {
      print('=== Testing OTP Verification ===');
      print('OTP: $otp, Registration: $isRegistration');
    }
    
    try {
      if (isRegistration) {
        final user = await FirebaseRealtimeAuthService.verifyRegistrationOTP(otp);
        if (user != null) {
          if (kDebugMode) print('✅ Registration OTP verified for user: ${user.uid}');
        } else {
          if (kDebugMode) print('❌ Registration OTP verification failed');
        }
      } else {
        final user = await FirebaseRealtimeAuthService.verifyLoginOTP(otp);
        if (user != null) {
          if (kDebugMode) print('✅ Login OTP verified for user: ${user.uid}');
        } else {
          if (kDebugMode) print('❌ Login OTP verification failed');
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ OTP verification test failed: $e');
    }
  }
  
  /// Test resend OTP functionality
  static Future<void> testResendOTP() async {
    if (kDebugMode) {
      print('=== Testing Resend OTP ===');
    }
    
    try {
      await FirebaseRealtimeAuthService.resendRegistrationOTP();
      if (kDebugMode) print('✅ Resend OTP successful');
    } catch (e) {
      if (kDebugMode) print('❌ Resend OTP test failed: $e');
    }
  }
  
  /// Run all tests
  static Future<void> runAllTests() async {
    if (kDebugMode) {
      print('🚀 Starting Firebase Real-time Auth Tests...');
      print('');
    }
    
    await testRegistrationOTP();
    await Future.delayed(const Duration(seconds: 2));
    
    await testLoginOTP();
    await Future.delayed(const Duration(seconds: 2));
    
    await testResendOTP();
    
    if (kDebugMode) {
      print('');
      print('🏁 Firebase Real-time Auth Tests completed!');
      print('Note: OTP verification tests require manual input of received OTP codes.');
    }
  }
}
