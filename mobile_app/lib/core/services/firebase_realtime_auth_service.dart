import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'firestore_service.dart';
import 'dart:convert';

/// Enhanced Firebase Auth Service with real-time OTP verification
class FirebaseRealtimeAuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Storage keys for session data
  static const String _registrationDataKey = 'firebase_registration_data';
  static const String _verificationIdKey = 'firebase_verification_id';
  static const String _registrationVerificationIdKey = 'firebase_registration_verification_id';
  static const String _tempRegistrationDataKey = 'temp_registration_data';
  static const String _resendTokenKey = 'resend_token';

  // Callbacks for real-time updates
  static Function(String)? _onCodeSent;
  static Function(String)? _onVerificationFailed;
  static Function()? _onVerificationCompleted;
  static Function(User)? _onRegistrationSuccess;

  /// Send OTP for registration with real-time callbacks
  static Future<void> sendRegistrationOTP({
    required String phoneNumber,
    required String name,
    required String email,
    required String officeName,
    required String designation,
    Function(String)? onCodeSent,
    Function(String)? onVerificationFailed,
    Function()? onVerificationCompleted,
    Function(User)? onRegistrationSuccess,
  }) async {
    try {
      // Store callbacks
      _onCodeSent = onCodeSent;
      _onVerificationFailed = onVerificationFailed;
      _onVerificationCompleted = onVerificationCompleted;
      _onRegistrationSuccess = onRegistrationSuccess;

      // Store temporary registration data
      final prefs = await SharedPreferences.getInstance();
      final registrationData = {
        'name': name,
        'email': email,
        'officeName': officeName,
        'designation': designation,
        'phoneNumber': phoneNumber,
      };
      await prefs.setString(_tempRegistrationDataKey, jsonEncode(registrationData));

      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification completed (Android only)
          try {
            final user = await _completeRegistration(
                credential, name, email, officeName, designation, phoneNumber);
            if (user != null) {
              _onRegistrationSuccess?.call(user);
              _onVerificationCompleted?.call();
            }
          } catch (e) {
            _onVerificationFailed?.call('Auto-verification failed: ${e.toString()}');
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          String errorMessage;
          if (e.code == 'billing-not-enabled') {
            errorMessage = 'Firebase SMS billing is not enabled. Please enable billing in Firebase Console or use a test phone number.';
          } else if (e.code == 'invalid-phone-number') {
            errorMessage = 'The phone number format is invalid. Please check and try again.';
          } else if (e.code == 'too-many-requests') {
            errorMessage = 'Too many requests. Please try again later.';
          } else if (e.code == 'quota-exceeded') {
            errorMessage = 'SMS quota exceeded. Please try again later.';
          } else {
            errorMessage = 'Phone verification failed: ${e.message ?? 'Unknown error'}';
          }
          _onVerificationFailed?.call(errorMessage);
        },
        codeSent: (String verificationId, int? resendToken) async {
          // Store verification ID and resend token for later use
          await prefs.setString(_registrationVerificationIdKey, verificationId);
          if (resendToken != null) {
            await prefs.setInt(_resendTokenKey, resendToken);
          }
          _onCodeSent?.call('OTP sent successfully to $phoneNumber');
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Auto-retrieval timeout - store the verification ID
          prefs.setString(_registrationVerificationIdKey, verificationId);
        },
        timeout: const Duration(seconds: 60),
      );
    } catch (e) {
      final errorMessage = 'Failed to send registration OTP: ${e.toString()}';
      _onVerificationFailed?.call(errorMessage);
      throw Exception(errorMessage);
    }
  }

  /// Verify OTP for registration with real-time feedback
  static Future<User?> verifyRegistrationOTP(String otp) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final verificationId = prefs.getString(_registrationVerificationIdKey);
      final tempDataString = prefs.getString(_tempRegistrationDataKey);

      if (verificationId == null) {
        throw Exception('Verification ID not found. Please try again.');
      }

      if (tempDataString == null) {
        throw Exception('Registration data not found. Please try again.');
      }

      // Parse registration data
      final registrationData = jsonDecode(tempDataString) as Map<String, dynamic>;
      final name = registrationData['name'] as String;
      final email = registrationData['email'] as String;
      final officeName = registrationData['officeName'] as String;
      final designation = registrationData['designation'] as String;
      final phoneNumber = registrationData['phoneNumber'] as String;

      // Create credential and sign in
      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );

      final user = await _completeRegistration(
          credential, name, email, officeName, designation, phoneNumber);

      if (user != null) {
        // Clear temporary data
        await prefs.remove(_registrationVerificationIdKey);
        await prefs.remove(_tempRegistrationDataKey);
        await prefs.remove(_resendTokenKey);
        
        _onRegistrationSuccess?.call(user);
      }

      return user;
    } catch (e) {
      if (e is FirebaseAuthException) {
        String errorMessage;
        switch (e.code) {
          case 'invalid-verification-code':
            errorMessage = 'Invalid OTP. Please check and try again.';
            break;
          case 'session-expired':
            errorMessage = 'OTP has expired. Please request a new one.';
            break;
          case 'too-many-requests':
            errorMessage = 'Too many attempts. Please try again later.';
            break;
          default:
            errorMessage = 'Verification failed: ${e.message ?? 'Unknown error'}';
        }
        throw Exception(errorMessage);
      }
      throw Exception('Failed to verify registration OTP: ${e.toString()}');
    }
  }

  /// Resend OTP for registration
  static Future<void> resendRegistrationOTP() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tempDataString = prefs.getString(_tempRegistrationDataKey);
      final resendToken = prefs.getInt(_resendTokenKey);

      if (tempDataString == null) {
        throw Exception('Registration data not found. Please start registration again.');
      }

      // Parse registration data
      final registrationData = jsonDecode(tempDataString) as Map<String, dynamic>;
      final name = registrationData['name'] as String;
      final email = registrationData['email'] as String;
      final officeName = registrationData['officeName'] as String;
      final designation = registrationData['designation'] as String;
      final phoneNumber = registrationData['phoneNumber'] as String;

      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        forceResendingToken: resendToken,
        verificationCompleted: (PhoneAuthCredential credential) async {
          try {
            final user = await _completeRegistration(
                credential, name, email, officeName, designation, phoneNumber);
            if (user != null) {
              _onRegistrationSuccess?.call(user);
              _onVerificationCompleted?.call();
            }
          } catch (e) {
            _onVerificationFailed?.call('Auto-verification failed: ${e.toString()}');
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          String errorMessage;
          if (e.code == 'too-many-requests') {
            errorMessage = 'Too many requests. Please wait before requesting another OTP.';
          } else {
            errorMessage = 'Failed to resend OTP: ${e.message ?? 'Unknown error'}';
          }
          _onVerificationFailed?.call(errorMessage);
        },
        codeSent: (String verificationId, int? newResendToken) async {
          await prefs.setString(_registrationVerificationIdKey, verificationId);
          if (newResendToken != null) {
            await prefs.setInt(_resendTokenKey, newResendToken);
          }
          _onCodeSent?.call('OTP resent successfully');
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          prefs.setString(_registrationVerificationIdKey, verificationId);
        },
        timeout: const Duration(seconds: 60),
      );
    } catch (e) {
      final errorMessage = 'Failed to resend OTP: ${e.toString()}';
      _onVerificationFailed?.call(errorMessage);
      throw Exception(errorMessage);
    }
  }

  /// Complete registration process
  static Future<User?> _completeRegistration(
    PhoneAuthCredential credential,
    String name,
    String email,
    String officeName,
    String designation,
    String phoneNumber,
  ) async {
    try {
      // Sign in with credential
      final userCredential = await _auth.signInWithCredential(credential);
      final user = userCredential.user;

      if (user != null) {
        // Update user profile
        await user.updateDisplayName(name);
        
        // Create Firestore document
        await FirestoreService.createUserDocument(
          uid: user.uid,
          name: name,
          email: email,
          phoneNumber: user.phoneNumber ?? phoneNumber,
          officeName: officeName,
          designation: designation,
        );

        // Store registration data locally
        final prefs = await SharedPreferences.getInstance();
        final registrationData = {
          'name': name,
          'email': email,
          'phoneNumber': user.phoneNumber ?? phoneNumber,
          'officeName': officeName,
          'designation': designation,
          'registrationDate': DateTime.now().toIso8601String(),
        };
        await prefs.setString(_registrationDataKey, jsonEncode(registrationData));

        // Sign out the user after registration (they need to login separately)
        await _auth.signOut();
      }

      return user;
    } catch (e) {
      throw Exception('Failed to complete registration: ${e.toString()}');
    }
  }

  /// Send OTP for login with real-time callbacks
  static Future<void> sendLoginOTP({
    required String phoneNumber,
    Function(String)? onCodeSent,
    Function(String)? onVerificationFailed,
    Function()? onVerificationCompleted,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          try {
            await _auth.signInWithCredential(credential);
            onVerificationCompleted?.call();
          } catch (e) {
            onVerificationFailed?.call('Auto-verification failed: ${e.toString()}');
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          String errorMessage;
          if (e.code == 'billing-not-enabled') {
            errorMessage = 'Firebase SMS billing is not enabled.';
          } else if (e.code == 'invalid-phone-number') {
            errorMessage = 'Invalid phone number format.';
          } else if (e.code == 'too-many-requests') {
            errorMessage = 'Too many requests. Please try again later.';
          } else {
            errorMessage = 'Phone verification failed: ${e.message ?? 'Unknown error'}';
          }
          onVerificationFailed?.call(errorMessage);
        },
        codeSent: (String verificationId, int? resendToken) async {
          await prefs.setString(_verificationIdKey, verificationId);
          onCodeSent?.call('OTP sent successfully');
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          prefs.setString(_verificationIdKey, verificationId);
        },
        timeout: const Duration(seconds: 60),
      );
    } catch (e) {
      final errorMessage = 'Failed to send login OTP: ${e.toString()}';
      onVerificationFailed?.call(errorMessage);
      throw Exception(errorMessage);
    }
  }

  /// Verify OTP for login
  static Future<User?> verifyLoginOTP(String otp) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final verificationId = prefs.getString(_verificationIdKey);

      if (verificationId == null) {
        throw Exception('Verification ID not found. Please try again.');
      }

      // Create credential and sign in
      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );

      final userCredential = await _auth.signInWithCredential(credential);
      final user = userCredential.user;

      if (user != null) {
        // Update last login in Firestore
        await FirestoreService.updateLastLogin(user.uid);

        // Clear verification ID
        await prefs.remove(_verificationIdKey);
      }

      return user;
    } catch (e) {
      if (e is FirebaseAuthException) {
        String errorMessage;
        switch (e.code) {
          case 'invalid-verification-code':
            errorMessage = 'Invalid OTP. Please check and try again.';
            break;
          case 'session-expired':
            errorMessage = 'OTP has expired. Please request a new one.';
            break;
          case 'too-many-requests':
            errorMessage = 'Too many attempts. Please try again later.';
            break;
          default:
            errorMessage = 'Verification failed: ${e.message ?? 'Unknown error'}';
        }
        throw Exception(errorMessage);
      }
      throw Exception('Failed to verify login OTP: ${e.toString()}');
    }
  }

  /// Clear all stored data
  static Future<void> clearStoredData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_verificationIdKey);
    await prefs.remove(_registrationVerificationIdKey);
    await prefs.remove(_tempRegistrationDataKey);
    await prefs.remove(_resendTokenKey);
  }

  /// Get current user
  static User? getCurrentUser() {
    return _auth.currentUser;
  }

  /// Sign out
  static Future<void> signOut() async {
    await _auth.signOut();
    await clearStoredData();
  }
}
