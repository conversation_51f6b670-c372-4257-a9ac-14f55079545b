import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/quiz_attempt_model.dart';
import '../models/exam_model.dart';

/// Service for managing quiz attempts
class QuizAttemptService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Get current user ID
  String? get currentUserId => _auth.currentUser?.uid;

  /// Start a new quiz attempt
  Future<String> startQuizAttempt(ExamModel exam) async {
    if (currentUserId == null) {
      throw Exception('User not authenticated');
    }

    try {
      final attempt = QuizAttemptModel(
        id: '', // Will be set by Firestore
        userId: currentUserId!,
        examId: exam.id,
        examName: exam.displayName,
        examType: exam.examType,
        attemptedAt: DateTime.now(),
        isCompleted: false,
        status: 'in_progress',
        totalQuestions: exam.numberOfQuestions,
      );

      final docRef = await _firestore
          .collection('quiz_attempts')
          .add(attempt.toFirestore());

      // Also increment the exam's total attempts
      await _incrementExamAttempts(exam.id);

      return docRef.id;
    } catch (e) {
      print('Error starting quiz attempt: $e');
      rethrow;
    }
  }

  /// Complete a quiz attempt
  Future<void> completeQuizAttempt({
    required String attemptId,
    required int score,
    required int correctAnswers,
    required int timeSpent,
    Map<String, dynamic>? answers,
  }) async {
    try {
      await _firestore.collection('quiz_attempts').doc(attemptId).update({
        'completedAt': FieldValue.serverTimestamp(),
        'score': score,
        'correctAnswers': correctAnswers,
        'timeSpent': timeSpent,
        'isCompleted': true,
        'status': 'completed',
        'answers': answers,
      });
    } catch (e) {
      print('Error completing quiz attempt: $e');
      rethrow;
    }
  }

  /// Abandon a quiz attempt
  Future<void> abandonQuizAttempt(String attemptId) async {
    try {
      await _firestore.collection('quiz_attempts').doc(attemptId).update({
        'status': 'abandoned',
      });
    } catch (e) {
      print('Error abandoning quiz attempt: $e');
      rethrow;
    }
  }

  /// Get user's recent quiz attempts
  Stream<List<QuizAttemptModel>> getUserRecentAttempts({int limit = 10}) {
    // Listen to auth state changes and return attempts when user is authenticated
    return _auth.authStateChanges().asyncMap((user) async {
      if (user == null) {
        print(
            '🚫 QuizAttemptService: No authenticated user, returning empty list');
        return <QuizAttemptModel>[];
      }

      print(
          '🔍 QuizAttemptService: Getting recent attempts for user: ${user.uid}');

      try {
        final snapshot = await _firestore
            .collection('quiz_attempts')
            .where('userId', isEqualTo: user.uid)
            .orderBy('attemptedAt', descending: true)
            .limit(limit)
            .get();

        print(
            '📊 QuizAttemptService: Found ${snapshot.docs.length} quiz attempts');

        final attempts = snapshot.docs
            .map((doc) => QuizAttemptModel.fromFirestore(doc))
            .toList();

        for (final attempt in attempts) {
          print(
              '📝 Attempt: ${attempt.examName} - ${attempt.status} - ${attempt.formattedAttemptDate}');
        }

        return attempts;
      } catch (e) {
        print('❌ QuizAttemptService: Error getting attempts: $e');
        return <QuizAttemptModel>[];
      }
    });
  }

  /// Get user's attempts for a specific exam
  Stream<List<QuizAttemptModel>> getUserExamAttempts(String examId) {
    if (currentUserId == null) {
      return Stream.value([]);
    }

    return _firestore
        .collection('quiz_attempts')
        .where('userId', isEqualTo: currentUserId)
        .where('examId', isEqualTo: examId)
        .orderBy('attemptedAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => QuizAttemptModel.fromFirestore(doc))
          .toList();
    });
  }

  /// Get user's best score for an exam
  Future<QuizAttemptModel?> getUserBestScore(String examId) async {
    if (currentUserId == null) return null;

    try {
      final snapshot = await _firestore
          .collection('quiz_attempts')
          .where('userId', isEqualTo: currentUserId)
          .where('examId', isEqualTo: examId)
          .where('isCompleted', isEqualTo: true)
          .orderBy('score', descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return QuizAttemptModel.fromFirestore(snapshot.docs.first);
      }
      return null;
    } catch (e) {
      print('Error getting user best score: $e');
      return null;
    }
  }

  /// Get user's quiz statistics
  Future<Map<String, dynamic>> getUserQuizStats() async {
    if (currentUserId == null) {
      return {
        'totalAttempts': 0,
        'completedQuizzes': 0,
        'averageScore': 0.0,
        'totalTimeSpent': 0,
      };
    }

    try {
      final snapshot = await _firestore
          .collection('quiz_attempts')
          .where('userId', isEqualTo: currentUserId)
          .get();

      final attempts = snapshot.docs
          .map((doc) => QuizAttemptModel.fromFirestore(doc))
          .toList();

      final completedAttempts = attempts.where((a) => a.isCompleted).toList();

      double averageScore = 0.0;
      int totalTimeSpent = 0;

      if (completedAttempts.isNotEmpty) {
        final totalScore = completedAttempts
            .where((a) => a.score != null)
            .fold(0, (sum, a) => sum + a.score!);

        final validScores =
            completedAttempts.where((a) => a.score != null).length;
        if (validScores > 0) {
          averageScore = totalScore / validScores;
        }

        totalTimeSpent = completedAttempts
            .where((a) => a.timeSpent != null)
            .fold(0, (sum, a) => sum + a.timeSpent!);
      }

      return {
        'totalAttempts': attempts.length,
        'completedQuizzes': completedAttempts.length,
        'averageScore': averageScore,
        'totalTimeSpent': totalTimeSpent,
      };
    } catch (e) {
      print('Error getting user quiz stats: $e');
      return {
        'totalAttempts': 0,
        'completedQuizzes': 0,
        'averageScore': 0.0,
        'totalTimeSpent': 0,
      };
    }
  }

  /// Check if user has attempted an exam
  Future<bool> hasUserAttemptedExam(String examId) async {
    if (currentUserId == null) return false;

    try {
      final snapshot = await _firestore
          .collection('quiz_attempts')
          .where('userId', isEqualTo: currentUserId)
          .where('examId', isEqualTo: examId)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      print('Error checking if user attempted exam: $e');
      return false;
    }
  }

  /// Get user's last attempt for an exam
  Future<QuizAttemptModel?> getUserLastAttempt(String examId) async {
    if (currentUserId == null) return null;

    try {
      final snapshot = await _firestore
          .collection('quiz_attempts')
          .where('userId', isEqualTo: currentUserId)
          .where('examId', isEqualTo: examId)
          .orderBy('attemptedAt', descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return QuizAttemptModel.fromFirestore(snapshot.docs.first);
      }
      return null;
    } catch (e) {
      print('Error getting user last attempt: $e');
      return null;
    }
  }

  /// Increment exam's total attempts count
  Future<void> _incrementExamAttempts(String examId) async {
    try {
      final examRef = _firestore.collection('exams').doc(examId);

      await _firestore.runTransaction((transaction) async {
        final examDoc = await transaction.get(examRef);

        if (examDoc.exists) {
          final currentAttempts = examDoc.data()?['totalAttempts'] ?? 0;
          transaction.update(examRef, {
            'totalAttempts': currentAttempts + 1,
            'updatedAt': FieldValue.serverTimestamp(),
          });
        }
      });
    } catch (e) {
      print('Error incrementing exam attempts: $e');
    }
  }

  /// Delete a quiz attempt
  Future<void> deleteQuizAttempt(String attemptId) async {
    try {
      await _firestore.collection('quiz_attempts').doc(attemptId).delete();
    } catch (e) {
      print('Error deleting quiz attempt: $e');
      rethrow;
    }
  }

  /// Get all attempts for admin (for analytics)
  Stream<List<QuizAttemptModel>> getAllAttempts({int limit = 100}) {
    return _firestore
        .collection('quiz_attempts')
        .orderBy('attemptedAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => QuizAttemptModel.fromFirestore(doc))
          .toList();
    });
  }

  /// Get attempts for a specific exam (for admin analytics)
  Stream<List<QuizAttemptModel>> getExamAttempts(String examId,
      {int limit = 50}) {
    return _firestore
        .collection('quiz_attempts')
        .where('examId', isEqualTo: examId)
        .orderBy('attemptedAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => QuizAttemptModel.fromFirestore(doc))
          .toList();
    });
  }
}
